-- FORCE ALTER COLUMNS - працює навіть на існуючій БД
-- Запускається кожен раз при старті PostgreSQL

\echo 'Starting column migration check...'

-- Check and alter columns if they exist
DO $$
DECLARE
    action_exists boolean := false;
    details_exists boolean := false;
BEGIN
    -- Check if old 'action' column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_activities' 
        AND column_name = 'action'
    ) INTO action_exists;
    
    -- Check if old 'details' column exists  
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_activities' 
        AND column_name = 'details'
    ) INTO details_exists;
    
    -- Rename action to activity_type if it exists
    IF action_exists THEN
        ALTER TABLE user_activities RENAME COLUMN action TO activity_type;
        RAISE NOTICE 'Column action renamed to activity_type';
    ELSE
        RAISE NOTICE 'Column action does not exist or already renamed';
    END IF;
    
    -- Rename details to activity_data if it exists
    IF details_exists THEN
        ALTER TABLE user_activities RENAME COLUMN details TO activity_data;
        RAISE NOTICE 'Column details renamed to activity_data';
    ELSE
        RAISE NOTICE 'Column details does not exist or already renamed';
    END IF;
    
    RAISE NOTICE 'Column migration completed successfully';
END
$$;

-- Verify the final schema
\echo 'Verifying final schema:'
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'user_activities' 
AND column_name IN ('activity_type', 'activity_data', 'action', 'details')
ORDER BY column_name;