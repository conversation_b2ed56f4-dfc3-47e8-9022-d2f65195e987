version: '3.8'

# ===============================================================================
# GADS SUPERCHARGE - PRODUCTION DEPLOYMENT v1.0
# ===============================================================================
# 🎯 GOOGLE ADS AUTOMATION PLATFORM:
# ✅ SERVICES: PostgreSQL, Redis, Backend API, Frontend React, MailHog Email
# ✅ DOMAINS: gads-supercharge.online (frontend), api.gads-supercharge.online (backend)
# ✅ EMAIL: mail.gads-supercharge.online (MailHog web interface for form emails)
# ✅ SECURITY: JWT authentication, bcrypt password hashing, SQL injection protection
# ✅ FEATURES: 12+ Google Ads script generators, bilingual support (UK/EN)
# ✅ DATABASE: PostgreSQL with 500+ translation keys and user management
# ✅ CACHE: Redis for session management and performance optimization
# ===============================================================================
# 🚀 DEPLOYMENT: Copy to Coolify → Deploy → Access https://gads-supercharge.online
# 📧 EMAIL TEST: https://mail.gads-supercharge.online:8025 (view form submissions)
# 🔑 ADMIN LOGIN: Create user via /register then promote to admin in database
# 📊 TOOLS: Budget Monitor, Campaign Performance, Keyword Analysis, and more
# ===============================================================================

networks:
  default:
    external: true
    name: coolify

volumes:
  postgres_data:
  redis_data:
  mailhog_data:
  backend_logs:
  backend_uploads:

services:
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: gads_db
      POSTGRES_USER: gads_user
      POSTGRES_PASSWORD: supersecurepassword123456
      POSTGRES_HOST_AUTH_METHOD: trust
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gads_user -d gads_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "coolify.managed=true"

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"

  backend:
    image: node:18-alpine
    working_dir: /app/backend
    command: sh -c "ls -la /app && ls -la /app/backend && sleep 30 && npm install && npm start"
    volumes:
      - .:/app
      - backend_logs:/app/backend/logs
      - backend_uploads:/app/backend/uploads
    restart: unless-stopped
    expose:
      - "3001"
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gads_db
      DB_USER: gads_user
      DB_PASSWORD: supersecurepassword123456
      JWT_SECRET: supersecurejwtsecret1234567890abcdef
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CORS_ORIGIN: https://gads-supercharge.online,https://www.gads-supercharge.online
      SMTP_HOST: mailhog
      SMTP_PORT: 1025
      EMAIL_FROM: <EMAIL>
      ADMIN_EMAIL: <EMAIL>
      DOMAIN: gads-supercharge.online
      FRONTEND_URL: https://gads-supercharge.online
      API_URL: https://api.gads-supercharge.online
    depends_on:
      - postgres
      - redis
      - mailhog
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "coolify.managed=true"
      - "coolify.url=api.gads-supercharge.online"
      - "coolify.port=3001"
      - "coolify.https=true"

  frontend:
    image: node:18-alpine
    working_dir: /app
    command: sh -c "ls -la /app && sleep 30 && npm install && npm run build && npx serve -s dist -l 80"
    volumes:
      - .:/app
      - /app/backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      VITE_API_URL: https://api.gads-supercharge.online/api
      VITE_NODE_ENV: production
    depends_on:
      - backend
    labels:
      - "coolify.managed=true"
      - "coolify.url=gads-supercharge.online"
      - "coolify.port=80"
      - "coolify.https=true"

  mailhog:
    image: mailhog/mailhog:latest
    restart: unless-stopped
    environment:
      MH_STORAGE: maildir
      MH_MAILDIR_PATH: /tmp
    volumes:
      - mailhog_data:/tmp
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"
      - "coolify.url=mail.gads-supercharge.online"
      - "coolify.port=8025"
      - "coolify.https=true"