const { Pool } = require('./backend/node_modules/pg');
const bcrypt = require('./backend/node_modules/bcrypt');
const { v4: uuidv4 } = require('./backend/node_modules/uuid');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'gads_db',
  password: 'G4d5Str0ng',
  port: 5432,
});

async function createNewUsers() {
  try {
    // Generate complex password hashes
    const adminHash = await bcrypt.hash('AdminPass2024Strong', 10);
    const userHash = await bcrypt.hash('UserPass2024Complex', 10);
    const demoHash = await bcrypt.hash('DemoPass2024Secure', 10);
    
    // Delete existing users
    await pool.query('DELETE FROM users WHERE email LIKE \'%@test.com\' OR email LIKE \'%@gads-supercharge.today\'');
    
    // Create new users with complex passwords
    const adminId = uuidv4();
    const userId = uuidv4();
    const demoId = uuidv4();
    
    await pool.query(`
      INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
      VALUES 
        ($1, '<EMAIL>', $2, 'admin', 'en', NOW(), NOW()),
        ($3, '<EMAIL>', $4, 'user', 'en', NOW(), NOW()),
        ($5, '<EMAIL>', $6, 'user', 'en', NOW(), NOW())
    `, [adminId, adminHash, userId, userHash, demoId, demoHash]);
    
    console.log('✅ New users created successfully!');
    console.log('📧 <EMAIL> / AdminPass2024Strong');
    console.log('📧 <EMAIL> / UserPass2024Complex');
    console.log('📧 <EMAIL> / DemoPass2024Secure');
    
    // Test login for admin
    const testResult = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (testResult.rows.length > 0) {
      const isValidPassword = await bcrypt.compare('AdminPass2024Strong', testResult.rows[0].password_hash);
      console.log('🔐 Password validation test:', isValidPassword ? '✅ PASSED' : '❌ FAILED');
    }
    
    await pool.end();
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    await pool.end();
    process.exit(1);
  }
}

createNewUsers();