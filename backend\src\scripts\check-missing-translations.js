const { query: dbQ<PERSON>y } = require('../database/connection');

async function checkMissingTranslations() {
  console.log('🔍 Checking for missing translations...');
  
  try {
    // Get all content keys that don't have Ukrainian translations
    const missingUA = await dbQuery(`
      SELECT ck.key_name, ck.category, ck.description
      FROM content_keys ck
      LEFT JOIN content_translations ct ON ck.id = ct.content_key_id AND ct.language_code = 'ua'
      WHERE ct.id IS NULL
      ORDER BY ck.category, ck.key_name
    `);

    // Get all content keys that don't have English translations
    const missingEN = await dbQuery(`
      SELECT ck.key_name, ck.category, ck.description
      FROM content_keys ck
      LEFT JOIN content_translations ct ON ck.id = ct.content_key_id AND ct.language_code = 'en'
      WHERE ct.id IS NULL
      ORDER BY ck.category, ck.key_name
    `);

    console.log('\n📊 СТАТИСТИКА:');
    console.log(`❌ Відсутні українські переклади: ${missingUA.rows.length}`);
    console.log(`❌ Відсутні англійські переклади: ${missingEN.rows.length}`);

    if (missingUA.rows.length > 0) {
      console.log('\n🇺🇦 ВІДСУТНІ УКРАЇНСЬКІ ПЕРЕКЛАДИ:');
      console.log('==========================================');
      
      // Group by category
      const grouped = missingUA.rows.reduce((acc, row) => {
        if (!acc[row.category]) acc[row.category] = [];
        acc[row.category].push(row);
        return acc;
      }, {});

      Object.entries(grouped).forEach(([category, keys]) => {
        console.log(`\n📁 ${category.toUpperCase()} (${keys.length} keys):`);
        keys.forEach(key => {
          console.log(`   • ${key.key_name}`);
        });
      });
    }

    if (missingEN.rows.length > 0) {
      console.log('\n🇬🇧 ВІДСУТНІ АНГЛІЙСЬКІ ПЕРЕКЛАДИ:');
      console.log('==========================================');
      
      // Group by category
      const grouped = missingEN.rows.reduce((acc, row) => {
        if (!acc[row.category]) acc[row.category] = [];
        acc[row.category].push(row);
        return acc;
      }, {});

      Object.entries(grouped).forEach(([category, keys]) => {
        console.log(`\n📁 ${category.toUpperCase()} (${keys.length} keys):`);
        keys.forEach(key => {
          console.log(`   • ${key.key_name}`);
        });
      });
    }

    // Get total counts by category
    const categoryStats = await dbQuery(`
      SELECT 
        ck.category,
        COUNT(ck.id) as total_keys,
        COUNT(CASE WHEN ct_en.id IS NOT NULL THEN 1 END) as has_english,
        COUNT(CASE WHEN ct_ua.id IS NOT NULL THEN 1 END) as has_ukrainian
      FROM content_keys ck
      LEFT JOIN content_translations ct_en ON ck.id = ct_en.content_key_id AND ct_en.language_code = 'en'
      LEFT JOIN content_translations ct_ua ON ck.id = ct_ua.content_key_id AND ct_ua.language_code = 'ua'
      GROUP BY ck.category
      ORDER BY total_keys DESC
    `);

    console.log('\n📈 СТАТИСТИКА ПО КАТЕГОРІЯХ:');
    console.log('============================');
    categoryStats.rows.forEach(stat => {
      const enPercent = Math.round((stat.has_english / stat.total_keys) * 100);
      const uaPercent = Math.round((stat.has_ukrainian / stat.total_keys) * 100);
      console.log(`${stat.category.padEnd(20)} | ${stat.total_keys.toString().padStart(3)} keys | EN: ${enPercent}% | UA: ${uaPercent}%`);
    });

    return {
      missingUA: missingUA.rows,
      missingEN: missingEN.rows,
      categoryStats: categoryStats.rows
    };

  } catch (error) {
    console.error('❌ Error checking translations:', error);
    throw error;
  }
}

module.exports = { checkMissingTranslations };

// Run if called directly
if (require.main === module) {
  checkMissingTranslations().then(() => process.exit(0));
}