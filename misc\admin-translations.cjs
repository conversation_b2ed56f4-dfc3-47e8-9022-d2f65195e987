const { Pool } = require('pg');

const pool = new Pool({
  user: 'gads_user',
  host: 'localhost',
  database: 'gads_db',
  password: 'gads_password',
  port: 5432,
});

const adminTranslations = [
  {
    key: 'admin.settings_title',
    category: 'admin',
    description: 'Admin settings page title',
    en: 'Admin Settings',
    ua: 'Налаштування Адміністратора'
  },
  {
    key: 'admin.settings_subtitle',
    category: 'admin',
    description: 'Admin settings page subtitle',
    en: 'Manage tracking, SEO, users, and content',
    ua: 'Управління відстеженням, SEO, користувачами та контентом'
  },
  {
    key: 'admin.tracking_analytics',
    category: 'admin',
    description: 'Tracking & Analytics section',
    en: 'Tracking & Analytics',
    ua: 'Відстеження та аналітика'
  },
  {
    key: 'admin.seo_management',
    category: 'admin',
    description: 'SEO Management section',
    en: 'SEO Management',
    ua: 'Управління SEO'
  },
  {
    key: 'admin.user_management',
    category: 'admin',
    description: 'User Management section',
    en: 'User Management',
    ua: 'Управління користувачами'
  },
  {
    key: 'admin.content_management',
    category: 'admin',
    description: 'Content Management section',
    en: 'Content Management',
    ua: 'Управління контентом'
  },
  {
    key: 'admin.access_denied',
    category: 'admin',
    description: 'Access denied message',
    en: 'Access Denied',
    ua: 'Доступ заборонено'
  },
  {
    key: 'admin.admin_only',
    category: 'admin',
    description: 'Admin only access message',
    en: 'This section is for administrators only.',
    ua: 'Цей розділ доступний тільки адміністраторам.'
  }
];

async function addAdminTranslations() {
  const client = await pool.connect();
  
  try {
    console.log('Adding admin translation keys...');
    
    for (const translation of adminTranslations) {
      // Insert content key
      const keyResult = await client.query(`
        INSERT INTO content_keys (key_name, category, description, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT (key_name) DO UPDATE SET
          category = EXCLUDED.category,
          description = EXCLUDED.description,
          updated_at = NOW()
        RETURNING id
      `, [translation.key, translation.category, translation.description]);
      
      const keyId = keyResult.rows[0].id;
      console.log(`Added key: ${translation.key} (${keyId})`);
      
      // Insert English translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at)
        VALUES ($1, 'en', $2, NOW(), NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text,
          updated_at = NOW()
      `, [keyId, translation.en]);
      
      // Insert Ukrainian translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at)
        VALUES ($1, 'ua', $2, NOW(), NOW())
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text,
          updated_at = NOW()
      `, [keyId, translation.ua]);
      
      console.log(`  EN: ${translation.en}`);
      console.log(`  UA: ${translation.ua}`);
    }
    
    console.log('✅ All admin translations added successfully!');
    
  } catch (error) {
    console.error('❌ Error adding admin translations:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addAdminTranslations();