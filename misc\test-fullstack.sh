#!/bin/bash

# Full-Stack Testing Script for gAds Supercharge
# Tests database-driven multilingual content and user activity tracking

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test configuration
API_BASE="http://localhost:3001/api"
FRONTEND_URL="http://localhost:5173"
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="gads_db"
DB_USER="gads_user"

# Test credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
USER_EMAIL="<EMAIL>"
USER_PASSWORD="user123"

# Function to test service health
test_service_health() {
    print_status "Testing service health..."
    
    # Test backend health
    if curl -f -s "$API_BASE/../health" > /dev/null; then
        print_success "Backend API is healthy"
    else
        print_error "Backend API is not responding"
        return 1
    fi
    
    # Test frontend
    if curl -f -s "$FRONTEND_URL" > /dev/null; then
        print_success "Frontend is accessible"
    else
        print_error "Frontend is not accessible"
        return 1
    fi
    
    # Test database connection
    if docker-compose exec -T postgres pg_isready -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        print_success "Database is accessible"
    else
        print_error "Database is not accessible"
        return 1
    fi
}

# Function to test authentication
test_authentication() {
    print_status "Testing authentication..."
    
    # Test admin login
    local admin_response=$(curl -s -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}")
    
    if echo "$admin_response" | grep -q "token"; then
        print_success "Admin login successful"
        ADMIN_TOKEN=$(echo "$admin_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    else
        print_error "Admin login failed"
        echo "Response: $admin_response"
        return 1
    fi
    
    # Test user login
    local user_response=$(curl -s -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$USER_EMAIL\",\"password\":\"$USER_PASSWORD\"}")
    
    if echo "$user_response" | grep -q "token"; then
        print_success "User login successful"
        USER_TOKEN=$(echo "$user_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    else
        print_error "User login failed"
        echo "Response: $user_response"
        return 1
    fi
}

# Function to test multilingual content
test_multilingual_content() {
    print_status "Testing multilingual content..."
    
    # Test English content
    local en_response=$(curl -s "$API_BASE/content/en")
    if echo "$en_response" | grep -q "Dashboard"; then
        print_success "English content loaded successfully"
    else
        print_error "Failed to load English content"
        return 1
    fi
    
    # Test Ukrainian content
    local ua_response=$(curl -s "$API_BASE/content/ua")
    if echo "$ua_response" | grep -q "Панель керування"; then
        print_success "Ukrainian content loaded successfully"
    else
        print_error "Failed to load Ukrainian content"
        return 1
    fi
    
    # Test category-specific content
    local tools_response=$(curl -s "$API_BASE/content/en/category/tools")
    if echo "$tools_response" | grep -q "Telegram"; then
        print_success "Category-specific content loaded successfully"
    else
        print_error "Failed to load category-specific content"
        return 1
    fi
}

# Function to test language switching
test_language_switching() {
    print_status "Testing language switching..."
    
    if [ -z "$USER_TOKEN" ]; then
        print_error "User token not available for language switching test"
        return 1
    fi
    
    # Test language update
    local lang_response=$(curl -s -X PUT "$API_BASE/user/language" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -d '{"language":"ua","previousLanguage":"en"}')
    
    if echo "$lang_response" | grep -q "successfully"; then
        print_success "Language switching successful"
    else
        print_error "Language switching failed"
        echo "Response: $lang_response"
        return 1
    fi
}

# Function to test user activity tracking
test_activity_tracking() {
    print_status "Testing user activity tracking..."
    
    if [ -z "$USER_TOKEN" ]; then
        print_error "User token not available for activity tracking test"
        return 1
    fi
    
    # Get user activities
    local activities_response=$(curl -s "$API_BASE/activity/my?limit=5" \
        -H "Authorization: Bearer $USER_TOKEN")
    
    if echo "$activities_response" | grep -q "activities"; then
        print_success "User activity tracking working"
    else
        print_error "User activity tracking failed"
        echo "Response: $activities_response"
        return 1
    fi
    
    # Get activity statistics
    local stats_response=$(curl -s "$API_BASE/activity/stats" \
        -H "Authorization: Bearer $USER_TOKEN")
    
    if echo "$stats_response" | grep -q "totalActivities"; then
        print_success "Activity statistics working"
    else
        print_error "Activity statistics failed"
        return 1
    fi
}

# Function to test database content
test_database_content() {
    print_status "Testing database content..."
    
    # Test content keys count
    local keys_count=$(docker-compose exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM content_keys;" | tr -d ' ')
    
    if [ "$keys_count" -gt 50 ]; then
        print_success "Database has sufficient content keys ($keys_count)"
    else
        print_error "Database has insufficient content keys ($keys_count)"
        return 1
    fi
    
    # Test translations count
    local translations_count=$(docker-compose exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM content_translations;" | tr -d ' ')
    
    if [ "$translations_count" -gt 100 ]; then
        print_success "Database has sufficient translations ($translations_count)"
    else
        print_error "Database has insufficient translations ($translations_count)"
        return 1
    fi
    
    # Test Ukrainian translations specifically
    local ua_translations=$(docker-compose exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM content_translations WHERE language_code = 'ua';" | tr -d ' ')
    
    if [ "$ua_translations" -gt 50 ]; then
        print_success "Database has sufficient Ukrainian translations ($ua_translations)"
    else
        print_error "Database has insufficient Ukrainian translations ($ua_translations)"
        return 1
    fi
}

# Function to test admin functionality
test_admin_functionality() {
    print_status "Testing admin functionality..."
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_error "Admin token not available for admin functionality test"
        return 1
    fi
    
    # Test admin access to all activities
    local admin_activities=$(curl -s "$API_BASE/activity/all?limit=5" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    if echo "$admin_activities" | grep -q "activities"; then
        print_success "Admin can access all activities"
    else
        print_error "Admin cannot access all activities"
        return 1
    fi
    
    # Test system statistics
    local system_stats=$(curl -s "$API_BASE/activity/system-stats" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    if echo "$system_stats" | grep -q "overall"; then
        print_success "Admin can access system statistics"
    else
        print_error "Admin cannot access system statistics"
        return 1
    fi
}

# Function to run all tests
run_all_tests() {
    echo "🧪 gAds Supercharge - Full-Stack Testing"
    echo "========================================"
    
    local failed_tests=0
    
    # Run tests
    test_service_health || ((failed_tests++))
    test_authentication || ((failed_tests++))
    test_multilingual_content || ((failed_tests++))
    test_language_switching || ((failed_tests++))
    test_activity_tracking || ((failed_tests++))
    test_database_content || ((failed_tests++))
    test_admin_functionality || ((failed_tests++))
    
    echo ""
    echo "========================================"
    
    if [ $failed_tests -eq 0 ]; then
        print_success "All tests passed! ✅"
        echo ""
        echo "🎉 Full-stack architecture is working correctly!"
        echo "✅ Database-driven multilingual content"
        echo "✅ User authentication and session management"
        echo "✅ Activity tracking and analytics"
        echo "✅ Admin functionality"
        echo "✅ Real-time language switching"
    else
        print_error "$failed_tests test(s) failed! ❌"
        echo ""
        echo "Please check the logs above for details."
        exit 1
    fi
}

# Main execution
if [ $# -eq 0 ]; then
    run_all_tests
else
    case $1 in
        health)
            test_service_health
            ;;
        auth)
            test_authentication
            ;;
        content)
            test_multilingual_content
            ;;
        language)
            test_authentication
            test_language_switching
            ;;
        activity)
            test_authentication
            test_activity_tracking
            ;;
        database)
            test_database_content
            ;;
        admin)
            test_authentication
            test_admin_functionality
            ;;
        *)
            echo "Usage: $0 [health|auth|content|language|activity|database|admin]"
            exit 1
            ;;
    esac
fi
