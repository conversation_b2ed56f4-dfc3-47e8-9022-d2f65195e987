#!/bin/bash

# gAds Supercharge Coolify Deployment Script
# For DigitalOcean ARM64 Droplet

set -e

echo "🚀 Starting gAds Supercharge deployment on Coolify..."

# Configuration
COOLIFY_URL="https://gads-supercharge.online"
PROJECT_NAME="gads-supercharge"
DOMAIN="gads-supercharge.online"
API_DOMAIN="api.gads-supercharge.online"
MAIL_DOMAIN="mail.gads-supercharge.online"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_error "This script should not be run as root"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
log_info "Creating necessary directories..."
sudo mkdir -p /opt/gads/{postgres_data,redis_data,logs,uploads}
sudo chown -R $USER:$USER /opt/gads

# Check if .env.production exists
if [[ ! -f ".env.production" ]]; then
    log_error ".env.production file not found. Please create it first."
    exit 1
fi

# Load environment variables
source .env.production

# Validate required environment variables
required_vars=(
    "POSTGRES_PASSWORD"
    "JWT_SECRET"
    "FRONTEND_URL"
    "BACKEND_URL"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        log_error "Required environment variable $var is not set"
        exit 1
    fi
done

# Stop existing containers
log_info "Stopping existing containers..."
docker-compose -f docker-compose.production.yml down || true

# Pull latest images
log_info "Pulling latest images..."
docker-compose -f docker-compose.production.yml pull

# Build and start services
log_info "Building and starting services..."
docker-compose -f docker-compose.production.yml up -d --build

# Wait for services to be healthy
log_info "Waiting for services to be healthy..."
timeout=300
counter=0

while [[ $counter -lt $timeout ]]; do
    if docker-compose -f docker-compose.production.yml ps | grep -q "healthy"; then
        log_info "Services are healthy!"
        break
    fi
    
    if [[ $counter -eq $timeout ]]; then
        log_error "Services failed to become healthy within $timeout seconds"
        exit 1
    fi
    
    sleep 5
    counter=$((counter + 5))
    echo -n "."
done

# Display service status
log_info "Service status:"
docker-compose -f docker-compose.production.yml ps

# Display useful information
log_info "Deployment completed successfully!"
echo
echo "🌐 Application URLs:"
echo "   Frontend: $FRONTEND_URL"
echo "   Backend API: $BACKEND_URL"
echo "   Mail Service: $MAIL_DOMAIN"
echo
echo "📊 Monitoring:"
echo "   Docker logs: docker-compose -f docker-compose.production.yml logs -f"
echo "   Service status: docker-compose -f docker-compose.production.yml ps"
echo
echo "🔧 Management:"
echo "   Restart services: docker-compose -f docker-compose.production.yml restart"
echo "   Stop services: docker-compose -f docker-compose.production.yml down"
echo "   Update services: ./deploy.sh"
echo
echo "📁 Data locations:"
echo "   PostgreSQL: /opt/gads/postgres_data"
echo "   Redis: /opt/gads/redis_data"
echo "   Logs: /opt/gads/logs"
echo "   Uploads: /opt/gads/uploads"

# Check if services are accessible
log_info "Checking service accessibility..."
if curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/health" | grep -q "200"; then
    log_info "✅ Backend API is accessible"
else
    log_warn "⚠️ Backend API might not be accessible yet"
fi

if curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" | grep -q "200"; then
    log_info "✅ Frontend is accessible"
else
    log_warn "⚠️ Frontend might not be accessible yet"
fi

log_info "Deployment script completed!"
echo
echo "🎉 Your gAds Supercharge application is now deployed!"
echo "Visit $FRONTEND_URL to access your application."