const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: 'gads_user',
  host: 'localhost',
  database: 'gads_db',
  password: 'gads_password',
  port: 5432,
});

const toolsTranslations = {
  // Budget Updater
  'tools.budgetUpdater.title': {
    en: 'Google Ads Budget Updater',
    ua: 'Оновлювач бюджету Google Ads'
  },
  'tools.budgetUpdater.description': {
    en: 'Automatically increases campaign budgets up to a specified maximum, with optional Telegram notifications.',
    ua: 'Автоматично збільшує бюджети кампаній до вказаного максимуму з опціональними сповіщеннями в Telegram.'
  },
  'tools.budgetUpdater.sections.campaign': {
    en: 'Campaign Configuration',
    ua: 'Налаштування кампанії'
  },
  'tools.budgetUpdater.sections.budget': {
    en: 'Budget Parameters',
    ua: 'Параметри бюджету'
  },
  'tools.budgetUpdater.sections.notifications': {
    en: 'Notification Settings (Optional)',
    ua: 'Налаштування сповіщень (Опціонально)'
  },
  'tools.budgetUpdater.fields.campaignPattern': {
    en: 'Campaign Name Pattern',
    ua: 'Шаблон назви кампанії'
  },
  'tools.budgetUpdater.fields.campaignType': {
    en: 'Campaign Type (Optional)',
    ua: 'Тип кампанії (Опціонально)'
  },
  'tools.budgetUpdater.fields.maxBudget': {
    en: 'Maximum Budget',
    ua: 'Максимальний бюджет'
  },
  'tools.budgetUpdater.fields.minBudget': {
    en: 'Minimum Budget',
    ua: 'Мінімальний бюджет'
  },
  'tools.budgetUpdater.fields.maxIncrements': {
    en: 'Max Increments',
    ua: 'Макс. збільшень'
  },
  'tools.budgetUpdater.fields.incrementPercentage': {
    en: 'Increment Percentage',
    ua: 'Відсоток збільшення'
  },
  'tools.budgetUpdater.fields.maxBudgetIncrease': {
    en: 'Max Budget Increase',
    ua: 'Макс. збільшення бюджету'
  },
  'tools.budgetUpdater.fields.telegramToken': {
    en: 'Telegram Bot Token',
    ua: 'Токен Telegram бота'
  },
  'tools.budgetUpdater.fields.telegramChatId': {
    en: 'Telegram Chat ID',
    ua: 'ID чату Telegram'
  },
  'tools.budgetUpdater.tooltips.campaignPattern': {
    en: 'Enter the exact name or a pattern of the campaign(s). Case-sensitive.',
    ua: 'Введіть точну назву або шаблон кампанії(й). Враховується регістр.'
  },
  'tools.budgetUpdater.tooltips.campaignType': {
    en: 'Specify the campaign type if you want to ensure the script targets a specific type (e.g., PERFORMANCE_MAX). Leave empty to search across all types.',
    ua: 'Вкажіть тип кампанії, якщо хочете переконатися, що скрипт націлений на конкретний тип (наприклад, PERFORMANCE_MAX). Залиште порожнім для пошуку серед усіх типів.'
  },
  'tools.budgetUpdater.tooltips.maxBudget': {
    en: 'The absolute maximum budget the campaign should not exceed.',
    ua: 'Абсолютний максимальний бюджет, який кампанія не повинна перевищувати.'
  },
  'tools.budgetUpdater.tooltips.minBudget': {
    en: 'The minimum budget the campaign should not go below.',
    ua: 'Мінімальний бюджет, нижче якого кампанія не повинна опускатися.'
  },
  'tools.budgetUpdater.tooltips.maxIncrements': {
    en: 'Maximum number of times the budget can be increased.',
    ua: 'Максимальна кількість разів, коли бюджет може бути збільшений.'
  },
  'tools.budgetUpdater.tooltips.incrementPercentage': {
    en: 'Percentage (e.g., 0.1 for 10%) to increase budget by. Applied to current budget.',
    ua: 'Відсоток (наприклад, 0.1 для 10%) на який збільшувати бюджет. Застосовується до поточного бюджету.'
  },
  'tools.budgetUpdater.tooltips.maxBudgetIncrease': {
    en: 'Maximum percentage the budget can be increased by.',
    ua: 'Максимальний відсоток, на який може бути збільшений бюджет.'
  },
  'tools.budgetUpdater.tooltips.telegramToken': {
    en: 'Your Telegram Bot Token for notifications. Stored locally.',
    ua: 'Ваш токен Telegram бота для сповіщень. Зберігається локально.'
  },
  'tools.budgetUpdater.tooltips.telegramChatId': {
    en: 'Your Telegram Chat ID for notifications. Stored locally.',
    ua: 'Ваш ID чату Telegram для сповіщень. Зберігається локально.'
  },
  'tools.budgetUpdater.campaignTypes.all': {
    en: 'All Types (Recommended if unsure)',
    ua: 'Всі типи (Рекомендовано, якщо не впевнені)'
  },
  'tools.budgetUpdater.campaignTypes.search': {
    en: 'Search',
    ua: 'Пошук'
  },
  'tools.budgetUpdater.campaignTypes.shopping': {
    en: 'Shopping',
    ua: 'Торгівля'
  },
  'tools.budgetUpdater.campaignTypes.display': {
    en: 'Display',
    ua: 'Медійна'
  },
  'tools.budgetUpdater.campaignTypes.video': {
    en: 'Video',
    ua: 'Відео'
  },
  'tools.budgetUpdater.campaignTypes.app': {
    en: 'App',
    ua: 'Додаток'
  },
  'tools.budgetUpdater.campaignTypes.smart': {
    en: 'Smart',
    ua: 'Розумна'
  },
  'tools.budgetUpdater.campaignTypes.hotel': {
    en: 'Hotel',
    ua: 'Готель'
  },
  'tools.budgetUpdater.generated': {
    en: 'Generated Budget Updater Script',
    ua: 'Згенерований скрипт оновлення бюджету'
  },

  // Budget Monitor
  'tools.budgetMonitor.title': {
    en: 'Google Ads Budget Pace Monitor',
    ua: 'Монітор темпу бюджету Google Ads'
  },
  'tools.budgetMonitor.description': {
    en: 'Monitors the daily spending pace of specified Google Ads campaigns and sends alerts if they are significantly over or under budget. Helps prevent overspending and ensures campaigns utilize their allocated budget effectively.',
    ua: 'Відстежує темп щоденних витрат вказаних кампаній Google Ads і надсилає сповіщення, якщо вони значно перевищують або не досягають бюджету. Допомагає запобігти перевитратам і забезпечує ефективне використання виділеного бюджету.'
  },
  'tools.budgetMonitor.howItWorks': {
    en: 'How It Works',
    ua: 'Як це працює'
  },
  'tools.budgetMonitor.sections.campaigns': {
    en: 'Campaign Selection',
    ua: 'Вибір кампаній'
  },
  'tools.budgetMonitor.sections.thresholds': {
    en: 'Pacing Thresholds (% of Expected Spend)',
    ua: 'Пороги темпу (% від очікуваних витрат)'
  },
  'tools.budgetMonitor.sections.notifications': {
    en: 'Telegram Notifications',
    ua: 'Сповіщення Telegram'
  },
  'tools.budgetMonitor.fields.campaignNames': {
    en: 'Campaign Names to Monitor',
    ua: 'Назви кампаній для моніторингу'
  },
  'tools.budgetMonitor.fields.highThreshold': {
    en: 'High Pacing Threshold (%) - Alert if Over',
    ua: 'Високий поріг темпу (%) - Сповіщення при перевищенні'
  },
  'tools.budgetMonitor.fields.lowThreshold': {
    en: 'Low Pacing Threshold (%) - Alert if Under',
    ua: 'Низький поріг темпу (%) - Сповіщення при недостачі'
  },
  'tools.budgetMonitor.fields.telegramToken': {
    en: 'Telegram Bot Token',
    ua: 'Токен Telegram бота'
  },
  'tools.budgetMonitor.fields.telegramChatId': {
    en: 'Telegram Chat ID',
    ua: 'ID чату Telegram'
  },
  'tools.budgetMonitor.generated': {
    en: 'Generated Script',
    ua: 'Згенерований скрипт'
  }
};

async function addTranslations() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    for (const [keyName, translations] of Object.entries(toolsTranslations)) {
      console.log(`Adding translations for: ${keyName}`);
      
      // Insert or update content key
      const keyResult = await client.query(`
        INSERT INTO content_keys (key_name, category, description)
        VALUES ($1, $2, $3)
        ON CONFLICT (key_name) DO UPDATE SET
          category = EXCLUDED.category,
          description = EXCLUDED.description
        RETURNING id
      `, [keyName, 'tools', `Translation for ${keyName}`]);
      
      const keyId = keyResult.rows[0].id;
      
      // Insert or update English translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        VALUES ($1, $2, $3)
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text
      `, [keyId, 'en', translations.en]);
      
      // Insert or update Ukrainian translation
      await client.query(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        VALUES ($1, $2, $3)
        ON CONFLICT (content_key_id, language_code) DO UPDATE SET
          translation_text = EXCLUDED.translation_text
      `, [keyId, 'ua', translations.ua]);
    }
    
    await client.query('COMMIT');
    console.log('✅ All translations added successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error adding translations:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addTranslations();
