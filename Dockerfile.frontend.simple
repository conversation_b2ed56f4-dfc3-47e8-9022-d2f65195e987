# Stage 1: Build the React application
FROM node:18-alpine AS builder
WORKDIR /app

# Install build dependencies for canvas
RUN apk add --no-cache build-base g++ cairo-dev jpeg-dev pango-dev giflib-dev python3

# Add ARG for the API URL and set it as an environment variable
ARG VITE_API_URL
ENV VITE_API_URL=${VITE_API_URL}

COPY package*.json ./
RUN npm install
COPY . .

# Log the API URL to ensure it's set
RUN echo "Building frontend with API URL: $VITE_API_URL"
RUN npm run build

# Stage 2: Serve with Nginx
FROM nginx:1.25-alpine

# Copy the built assets from the builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy the Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Add curl for health checks
RUN apk add --no-cache curl

EXPOSE 80

# Health check (optional but good practice)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

CMD ["nginx", "-g", "daemon off;"]

