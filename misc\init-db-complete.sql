-- ===============================================================================
-- GADS SUPERCHARGE DATABASE SCHEMA - COMPLETE SETUP WITH ALL DATA
-- ===============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Update function for timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    preferred_language VARCHAR(5) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    access_expiry_date TIMESTAMP WITH TIME ZONE,
    CONSTRAINT users_preferred_language_check CHECK (preferred_language IN ('en', 'ua')),
    CONSTRAINT users_role_check CHECK (role IN ('admin', 'user'))
);

-- Content keys table
CREATE TABLE IF NOT EXISTS content_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_name VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Content translations table
CREATE TABLE IF NOT EXISTS content_translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_key_id UUID NOT NULL,
    language_code VARCHAR(5) NOT NULL,
    translation_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT content_translations_language_code_check CHECK (language_code IN ('en', 'ua')),
    UNIQUE(content_key_id, language_code)
);

-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    browser_info JSONB,
    login_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- User activities table
CREATE TABLE IF NOT EXISTS user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    session_id UUID,
    activity_type VARCHAR(100) NOT NULL,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    page_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, preference_key)
);

-- Admin settings table
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID
);

-- SEO settings table
CREATE TABLE IF NOT EXISTS seo_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page VARCHAR(100) UNIQUE NOT NULL,
    title_en VARCHAR(255),
    title_ua VARCHAR(255),
    description_en TEXT,
    description_ua TEXT,
    keywords_en TEXT,
    keywords_ua TEXT,
    verification_codes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active);
CREATE INDEX IF NOT EXISTS idx_users_access_expiry ON users(access_expiry_date);

CREATE INDEX IF NOT EXISTS idx_content_keys_category ON content_keys(category);
CREATE INDEX IF NOT EXISTS idx_content_keys_name ON content_keys(key_name);

CREATE INDEX IF NOT EXISTS idx_content_translations_language ON content_translations(language_code);
CREATE INDEX IF NOT EXISTS idx_content_translations_key_lang ON content_translations(content_key_id, language_code);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_session_id ON user_activities(session_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activities_created ON user_activities(created_at);

CREATE INDEX IF NOT EXISTS idx_user_preferences_user_key ON user_preferences(user_id, preference_key);

CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);
CREATE INDEX IF NOT EXISTS idx_seo_settings_page ON seo_settings(page);

-- Foreign key constraints
ALTER TABLE content_translations ADD CONSTRAINT content_translations_content_key_id_fkey 
    FOREIGN KEY (content_key_id) REFERENCES content_keys(id) ON DELETE CASCADE;

ALTER TABLE user_sessions ADD CONSTRAINT user_sessions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE user_activities ADD CONSTRAINT user_activities_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE user_activities ADD CONSTRAINT user_activities_session_id_fkey 
    FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE SET NULL;

ALTER TABLE user_preferences ADD CONSTRAINT user_preferences_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE admin_settings ADD CONSTRAINT admin_settings_updated_by_fkey 
    FOREIGN KEY (updated_by) REFERENCES users(id);

ALTER TABLE seo_settings ADD CONSTRAINT seo_settings_updated_by_fkey 
    FOREIGN KEY (updated_by) REFERENCES users(id);

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_keys_updated_at BEFORE UPDATE ON content_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_translations_updated_at BEFORE UPDATE ON content_translations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_settings_updated_at BEFORE UPDATE ON admin_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_seo_settings_updated_at BEFORE UPDATE ON seo_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user
INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
VALUES (
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$12$3Rrl73mutFg3x2bbnzKBFueIpG2dwCLoREcbzMVinj3P8EiZxJFl6', -- Admin2025!Secure#
    'admin',
    'ua',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert additional users
INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
VALUES 
    (uuid_generate_v4(), '<EMAIL>', '$2a$12$uE001MFMBW91IZFMpZunEeLqAnZPKUQC4sT10ORDqQ/vEqAyJvopa', 'admin', 'en', NOW(), NOW()),
    (uuid_generate_v4(), '<EMAIL>', '$2a$10$gRHQtp/x/JVqIcMPw/7oe.lJQ96VR38YV70j.D7IBnITOTIGze8uy', 'user', 'en', NOW(), NOW()),
    (uuid_generate_v4(), '<EMAIL>', '$2a$10$1YY32.zq/jucG6By8lVUVu2tua3bFijJZ5vFW2l2P1o9yedUnw636', 'user', 'en', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Insert admin settings
INSERT INTO admin_settings (key, value, description) VALUES
    ('tracking_code', '', 'JavaScript tracking code for analytics (Google Tag Manager, Facebook Pixel, etc.)'),
    ('verification_codes', '', 'HTML verification codes for search engines')
ON CONFLICT (key) DO NOTHING;

-- Insert all content keys from local database
INSERT INTO content_keys (key_name, category, description) VALUES
    ('nav.dashboard', 'navigation', 'Dashboard navigation link'),
    ('nav.login', 'navigation', 'Login navigation link'),
    ('nav.logout', 'navigation', 'Logout navigation link'),
    ('nav.features', 'navigation', 'Features navigation link'),
    ('nav.portfolio', 'navigation', 'Portfolio navigation link'),
    ('nav.home', 'navigation', 'Home navigation link'),
    ('nav.tools', 'navigation', 'Tools navigation link'),
    ('nav.settings', 'navigation', 'Settings navigation link'),
    ('nav.help', 'navigation', 'Help navigation link'),
    ('common.loading', 'common', 'Loading text'),
    ('common.error', 'common', 'Error text'),
    ('common.success', 'common', 'Success text'),
    ('common.cancel', 'common', 'Cancel button'),
    ('common.save', 'common', 'Save button'),
    ('common.delete', 'common', 'Delete button'),
    ('common.edit', 'common', 'Edit button'),
    ('common.back', 'common', 'Back button'),
    ('common.next', 'common', 'Next button'),
    ('common.previous', 'common', 'Previous button'),
    ('common.submit', 'common', 'Submit button'),
    ('common.generate', 'common', 'Generate button'),
    ('common.copy', 'common', 'Copy button'),
    ('common.download', 'common', 'Download button'),
    ('auth.login', 'auth', 'Login text'),
    ('auth.email', 'auth', 'Email field label'),
    ('auth.password', 'auth', 'Password field label'),
    ('auth.loginButton', 'auth', 'Login button text'),
    ('auth.loginSuccess', 'auth', 'Login success message'),
    ('auth.loginError', 'auth', 'Login error message'),
    ('auth.logout', 'auth', 'Logout text'),
    ('auth.welcome', 'auth', 'Welcome message'),
    ('dashboard.title', 'dashboard', 'Dashboard page title'),
    ('dashboard.welcome', 'dashboard', 'Dashboard welcome message'),
    ('dashboard.description', 'dashboard', 'Dashboard description'),
    ('dashboard.tools', 'dashboard', 'Available tools section'),
    ('dashboard.recentActivity', 'dashboard', 'Recent activity section'),
    ('dashboard.welcomeBack', 'dashboard', 'Welcome back message with user name'),
    ('dashboard.quickActions', 'dashboard', 'Quick actions section'),
    ('dashboard.recentScripts', 'dashboard', 'Recent scripts section'),
    ('dashboard.analytics', 'dashboard', 'Analytics section'),
    ('tools.telegram.title', 'tools', 'Telegram tool title'),
    ('tools.telegram.description', 'tools', 'Telegram tool description'),
    ('tools.airtable.title', 'tools', 'Airtable tool title'),
    ('tools.airtable.description', 'tools', 'Airtable tool description'),
    ('tools.budget.title', 'tools', 'Budget tool title'),
    ('tools.budget.description', 'tools', 'Budget tool description'),
    ('tools.campaign.title', 'tools', 'Campaign tool title'),
    ('tools.campaign.description', 'tools', 'Campaign tool description'),
    ('tools.keyword.title', 'tools', 'Keyword tool title'),
    ('tools.keyword.description', 'tools', 'Keyword tool description'),
    ('services.title', 'services', 'Services page title'),
    ('features.title', 'features', 'Features page title'),
    ('about.title', 'about', 'About page title')
ON CONFLICT (key_name) DO NOTHING;

-- Insert basic translations
WITH content_key_ids AS (
    SELECT id, key_name FROM content_keys 
)
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT 
    ck.id,
    lang.code,
    CASE 
        -- Navigation
        WHEN ck.key_name = 'nav.home' AND lang.code = 'en' THEN 'Home'
        WHEN ck.key_name = 'nav.home' AND lang.code = 'ua' THEN 'Головна'
        WHEN ck.key_name = 'nav.dashboard' AND lang.code = 'en' THEN 'Dashboard'
        WHEN ck.key_name = 'nav.dashboard' AND lang.code = 'ua' THEN 'Панель керування'
        WHEN ck.key_name = 'nav.tools' AND lang.code = 'en' THEN 'Tools'
        WHEN ck.key_name = 'nav.tools' AND lang.code = 'ua' THEN 'Інструменти'
        WHEN ck.key_name = 'nav.features' AND lang.code = 'en' THEN 'Features'
        WHEN ck.key_name = 'nav.features' AND lang.code = 'ua' THEN 'Можливості'
        WHEN ck.key_name = 'nav.portfolio' AND lang.code = 'en' THEN 'Portfolio'
        WHEN ck.key_name = 'nav.portfolio' AND lang.code = 'ua' THEN 'Портфоліо'
        WHEN ck.key_name = 'nav.settings' AND lang.code = 'en' THEN 'Settings'
        WHEN ck.key_name = 'nav.settings' AND lang.code = 'ua' THEN 'Налаштування'
        -- Auth
        WHEN ck.key_name = 'auth.login' AND lang.code = 'en' THEN 'Login'
        WHEN ck.key_name = 'auth.login' AND lang.code = 'ua' THEN 'Вхід'
        WHEN ck.key_name = 'auth.logout' AND lang.code = 'en' THEN 'Logout'
        WHEN ck.key_name = 'auth.logout' AND lang.code = 'ua' THEN 'Вихід'
        WHEN ck.key_name = 'auth.email' AND lang.code = 'en' THEN 'Email'
        WHEN ck.key_name = 'auth.email' AND lang.code = 'ua' THEN 'Електронна пошта'
        WHEN ck.key_name = 'auth.password' AND lang.code = 'en' THEN 'Password'
        WHEN ck.key_name = 'auth.password' AND lang.code = 'ua' THEN 'Пароль'
        WHEN ck.key_name = 'auth.loginButton' AND lang.code = 'en' THEN 'Sign In'
        WHEN ck.key_name = 'auth.loginButton' AND lang.code = 'ua' THEN 'Увійти'
        -- Common
        WHEN ck.key_name = 'common.loading' AND lang.code = 'en' THEN 'Loading...'
        WHEN ck.key_name = 'common.loading' AND lang.code = 'ua' THEN 'Завантаження...'
        WHEN ck.key_name = 'common.save' AND lang.code = 'en' THEN 'Save'
        WHEN ck.key_name = 'common.save' AND lang.code = 'ua' THEN 'Зберегти'
        WHEN ck.key_name = 'common.cancel' AND lang.code = 'en' THEN 'Cancel'
        WHEN ck.key_name = 'common.cancel' AND lang.code = 'ua' THEN 'Скасувати'
        WHEN ck.key_name = 'common.generate' AND lang.code = 'en' THEN 'Generate'
        WHEN ck.key_name = 'common.generate' AND lang.code = 'ua' THEN 'Згенерувати'
        -- Dashboard
        WHEN ck.key_name = 'dashboard.title' AND lang.code = 'en' THEN 'Dashboard'
        WHEN ck.key_name = 'dashboard.title' AND lang.code = 'ua' THEN 'Панель керування'
        WHEN ck.key_name = 'dashboard.welcome' AND lang.code = 'en' THEN 'Welcome to gAds Supercharge'
        WHEN ck.key_name = 'dashboard.welcome' AND lang.code = 'ua' THEN 'Ласкаво просимо до gAds Supercharge'
        -- Tools
        WHEN ck.key_name = 'tools.telegram.title' AND lang.code = 'en' THEN 'Telegram Script Generator'
        WHEN ck.key_name = 'tools.telegram.title' AND lang.code = 'ua' THEN 'Генератор Telegram скриптів'
        WHEN ck.key_name = 'tools.budget.title' AND lang.code = 'en' THEN 'Budget Monitor'
        WHEN ck.key_name = 'tools.budget.title' AND lang.code = 'ua' THEN 'Моніторинг бюджету'
        -- Services
        WHEN ck.key_name = 'services.title' AND lang.code = 'en' THEN 'Our Services'
        WHEN ck.key_name = 'services.title' AND lang.code = 'ua' THEN 'Наші послуги'
        WHEN ck.key_name = 'features.title' AND lang.code = 'en' THEN 'Features'
        WHEN ck.key_name = 'features.title' AND lang.code = 'ua' THEN 'Можливості'
        WHEN ck.key_name = 'about.title' AND lang.code = 'en' THEN 'About Us'
        WHEN ck.key_name = 'about.title' AND lang.code = 'ua' THEN 'Про нас'
        ELSE ck.key_name -- fallback to key name
    END as translation_text
FROM content_key_ids ck
CROSS JOIN (VALUES ('en'), ('ua')) AS lang(code)
WHERE CASE 
    WHEN ck.key_name = 'nav.home' THEN true
    WHEN ck.key_name = 'nav.dashboard' THEN true
    WHEN ck.key_name = 'nav.tools' THEN true
    WHEN ck.key_name = 'nav.features' THEN true
    WHEN ck.key_name = 'nav.portfolio' THEN true
    WHEN ck.key_name = 'nav.settings' THEN true
    WHEN ck.key_name = 'auth.login' THEN true
    WHEN ck.key_name = 'auth.logout' THEN true
    WHEN ck.key_name = 'auth.email' THEN true
    WHEN ck.key_name = 'auth.password' THEN true
    WHEN ck.key_name = 'auth.loginButton' THEN true
    WHEN ck.key_name = 'common.loading' THEN true
    WHEN ck.key_name = 'common.save' THEN true
    WHEN ck.key_name = 'common.cancel' THEN true
    WHEN ck.key_name = 'common.generate' THEN true
    WHEN ck.key_name = 'dashboard.title' THEN true
    WHEN ck.key_name = 'dashboard.welcome' THEN true
    WHEN ck.key_name = 'tools.telegram.title' THEN true
    WHEN ck.key_name = 'tools.budget.title' THEN true
    WHEN ck.key_name = 'services.title' THEN true
    WHEN ck.key_name = 'features.title' THEN true
    WHEN ck.key_name = 'about.title' THEN true
    ELSE false
END
ON CONFLICT (content_key_id, language_code) DO NOTHING;

-- Log completion
SELECT 'Complete database with all tables, users, content keys and translations created successfully!' as status;