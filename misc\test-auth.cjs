const fetch = require('node-fetch');

async function testAuth() {
  try {
    console.log('🔐 Testing authentication...');
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin2025!Secure#'
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers));
    
    const data = await response.text();
    console.log('Response body:', data);
    
    if (response.ok) {
      console.log('✅ Authentication successful!');
      const jsonData = JSON.parse(data);
      console.log('Token received:', jsonData.token ? 'Yes' : 'No');
    } else {
      console.log('❌ Authentication failed');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testAuth();
