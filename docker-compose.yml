# ===============================================================================
# GADS SUPERCHARGE - PRODUCTION DEPLOYMENT v1.48-cors-fix
# ===============================================================================
# 🎯 GOOGLE ADS AUTOMATION PLATFORM
# ✅ DOMAINS: gads-supercharge.online, api.gads-supercharge.online, mail.gads-supercharge.online
# ✅ FEATURES: 12+ Google Ads script generators, bilingual support (UK/EN), 487+ translations
# ✅ SERVICES: PostgreSQL, Redis, Backend API, Frontend React, MailHog Email
# 🔑 ADMIN LOGIN: <EMAIL> / Admin2025!Secure#
# 📧 EMAIL TEST: https://mail.gads-supercharge.online (admin/mailhog123)
#
# 🔐 БЕЗПЕЧНИЙ ДЕПЛОЙ v1.48-cors-fix:
# - АВТОМАТИЧНО: Користувачі створюються при старті backend контейнера
# - ВИПРАВЛЕНО: CORS налаштування для frontend домену
# - ВИПРАВЛЕНО: Admin API SQL queries для сумісності з різними схемами БД
# - ВИПРАВЛЕНО: User management functionality з правильним API URL
# - ЗАХИСТ: Математична капча + MailHog Nginx Auth (admin/mailhog123)
# - КОНТЕНТ: Автозаповнення відсутніх переводів
#
# 🔄 ВЕРСІЇ ОБРАЗІВ:
# - Backend: v1.48-cors-fix ✅ (ВИПРАВЛЕНО CORS + SQL queries + автоматичне створення таблиць)
# - Frontend: v1.46-user-fix ✅ (ВИПРАВЛЕНО user management + правильний API URL)
# - PostgreSQL: v1.41-force-fix ✅ (FORCE ALTER TABLE в існуючій БД)
# - MailHog: v1.0.0 ✅ (власний образ) + Nginx Proxy v1.0.1 з Basic Auth (admin/mailhog123)
#
# 📋 ПІСЛЯ ДЕПЛОЮ:
# ✅ НІЧОГО НЕ ТРЕБА РОБИТИ - ВСЕ АВТОМАТИЧНО!
# ===============================================================================

version: '3.8'

services:
  postgres:
    # 🗄️ POSTGRESQL DATABASE - v1.41-force-fix
    # 🚨 FORCE ALTER TABLE: Виправляє існуючі таблиці без втрати даних
    # 📋 АВТОМАТИЧНО: Створює всі таблиці, індекси, користувачів при старті
    image: aprokudan/gads-supercharge-postgres:v1.41-force-fix
    platform: linux/amd64
    restart: unless-stopped
    environment:
      POSTGRES_DB: gads_db
      POSTGRES_USER: gads_user
      POSTGRES_PASSWORD: supersecurepassword123456
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    expose:
      - "5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gads_user -d gads_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    labels:
      - "coolify.managed=true"
      - "coolify.name=postgres"
      - "coolify.type=service"

  redis:
    # 🔄 REDIS CACHE - Для сесій та кешування
    image: redis:7-alpine
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "coolify.managed=true"
      - "coolify.name=redis"
      - "coolify.type=service"

  mailhog:
    # 📧 MAILHOG EMAIL TESTING - v1.0.0
    # 🔒 ЗАХИЩЕНО: Nginx proxy з HTTP Basic Auth (admin/mailhog123)
    image: aprokudan/gads-supercharge-mailhog:v1.0.0
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "1025"  # SMTP
      - "8025"  # Web UI
    labels:
      - "coolify.managed=true"
      - "coolify.name=mailhog"
      - "coolify.type=service"

  nginx-mailhog:
    # 🔒 NGINX PROXY для MailHog з HTTP Basic Auth
    image: aprokudan/gads-supercharge-nginx-mailhog:v1.0.1
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "80"
    depends_on:
      - mailhog
    labels:
      - "coolify.managed=true"
      - "coolify.name=nginx-mailhog"
      - "coolify.type=application"
      - "coolify.fqdn=mail.gads-supercharge.online"
      - "coolify.port=80"
      - "coolify.https=true"

  backend:
    # 🔧 BACKEND API - Node.js + Express + PostgreSQL
    # 🚨 v1.44-final-fix: АВТОМАТИЧНО створює користувачів при старті
    # 📋 БЕЗ РУЧНИХ ДІЙ: Користувачі створюються/оновлюються автоматично при старті
    image: aprokudan/gads-supercharge-backend:v1.48-cors-fix
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "3001"
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gads_db
      DB_USER: gads_user
      DB_PASSWORD: supersecurepassword123456
      JWT_SECRET: your-super-secret-jwt-key-change-in-production-2024
      REDIS_URL: redis://redis:6379
      CORS_ORIGIN: https://gads-supercharge.online,https://www.gads-supercharge.online
      SMTP_HOST: mailhog
      SMTP_PORT: 1025
      EMAIL_FROM: <EMAIL>
      ADMIN_EMAIL: <EMAIL>
      DOMAIN: gads-supercharge.online
      FRONTEND_URL: https://gads-supercharge.online
      API_URL: https://api.gads-supercharge.online
    depends_on:
      - postgres
      - redis
      - mailhog
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "coolify.managed=true"
      - "coolify.name=backend"
      - "coolify.type=application"
      - "coolify.fqdn=api.gads-supercharge.online"
      - "coolify.port=3001"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"

  frontend:
    # 🎨 FRONTEND - React + Vite + Nginx
    # 🚨 v1.46-user-fix: ВИПРАВЛЕНО user management + правильний API URL
    # 📋 Підключається до API: https://api.gads-supercharge.online/api
    image: aprokudan/gads-supercharge-frontend:v1.46-user-fix
    platform: linux/amd64
    restart: unless-stopped
    expose:
      - "80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "coolify.managed=true"
      - "coolify.name=frontend"
      - "coolify.type=application"
      - "coolify.fqdn=gads-supercharge.online"
      - "coolify.port=80"
      - "coolify.https=true"
      - "coolify.healthcheck.enabled=true"
      - "coolify.healthcheck.path=/health"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    driver: bridge
