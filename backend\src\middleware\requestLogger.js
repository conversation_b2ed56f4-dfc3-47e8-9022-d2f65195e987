const { v4: uuidv4 } = require('uuid');

/**
 * Request logging middleware
 */
function requestLogger(req, res, next) {
  // Add unique request ID
  req.id = uuidv4();
  
  // Add request ID to response headers
  res.setHeader('X-Request-ID', req.id);

  // Log request details
  const startTime = Date.now();
  
  console.log(`📥 ${req.method} ${req.originalUrl}`, {
    requestId: req.id,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    timestamp: new Date().toISOString()
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    console.log(`📤 ${req.method} ${req.originalUrl} - ${res.statusCode}`, {
      requestId: req.id,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.get('Content-Length'),
      timestamp: new Date().toISOString()
    });

    return originalEnd.apply(this, args);
  };

  next();
}

module.exports = {
  requestLogger
};
