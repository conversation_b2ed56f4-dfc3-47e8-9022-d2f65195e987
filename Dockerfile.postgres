FROM postgres:15-alpine

# Встановлюємо власника бази даних
ENV POSTGRES_DB=gads_db
ENV POSTGRES_USER=gads_user
ENV POSTGRES_PASSWORD=supersecurepassword123456

# Копіюємо актуальну схему з backend
COPY backend/database/init.sql /docker-entrypoint-initdb.d/01-init-schema.sql
COPY misc/complete-database-with-schema-20250715_225120.sql /docker-entrypoint-initdb.d/02-init-data.sql
COPY misc/force-alter-columns.sql /docker-entrypoint-initdb.d/99-force-alter-columns.sql

# Встановлюємо права доступу
RUN chmod 755 /docker-entrypoint-initdb.d/*.sql

# Відкриваємо порт
EXPOSE 5432

# Запускаємо PostgreSQL
CMD ["postgres"]