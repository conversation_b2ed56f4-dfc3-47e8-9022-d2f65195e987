import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Hook to handle analytics initialization and page view tracking
 */
export const useAnalytics = () => {
  const location = useLocation();

  // Initialize analytics and track page view on route change
  useEffect(() => {
    let mounted = true;
    let timer: NodeJS.Timeout;

    const initAndTrack = async () => {
      try {
        const { initAnalytics, trackPageView } = await import('../utils/analytics');
        
        // Only proceed if component is still mounted
        if (!mounted) return;
        
        try {
          await initAnalytics();
          
          // Small delay to ensure the title is updated
          timer = setTimeout(() => {
            if (mounted) {
              trackPageView(location.pathname, document.title);
            }
          }, 100);
        } catch (error) {
          console.error('Analytics initialization failed:', error);
        }
      } catch (error) {
        console.error('Failed to load analytics module:', error);
      }
    };

    // Only initialize in production or if explicitly enabled in development
    if (process.env.NODE_ENV === 'production' || process.env.REACT_APP_ENABLE_ANALYTICS === 'true') {
      initAndTrack();
    }

    return () => {
      mounted = false;
      if (timer) clearTimeout(timer);
    };
  }, [location.pathname]);
};

/**
 * Hook to track custom events
 * @returns Function to track events
 */
export const useTrackEvent = () => {
  return useCallback((category: string, action: string, label?: string, value?: number) => {
    import('../utils/analytics')
      .then(({ trackEvent }) => {
        trackEvent(category, action, label, value);
      })
      .catch(error => {
        console.error('Error tracking event:', error);
      });
  }, []);
};
