import React from 'react';
import { Navigate } from 'react-router-dom';
import { useApiAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useApiAuth();

  console.log('🛡️ ProtectedRoute: isLoading =', isLoading, 'isAuthenticated =', isAuthenticated);

  // Show loading state while checking authentication
  if (isLoading) {
    console.log('🛡️ ProtectedRoute: Showing loading...');
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    console.log('🛡️ ProtectedRoute: Not authenticated, redirecting to login...');
    return <Navigate to="/login" replace />;
  }

  console.log('🛡️ ProtectedRoute: Authenticated, rendering children...');
  // Render children if authenticated
  return <>{children}</>;
};

export default ProtectedRoute;
