# ===============================================================================
# GADS SUPERCHARGE - NGINX MAILHOG PROXY CONFIGURATION
# ===============================================================================
# 🎯 NGINX REVERSE PROXY FOR MAILHOG WITH HTTP BASIC AUTH
# ✅ SECURITY: HTTP Basic Authentication (admin/mailhog123)
# ✅ PROXY: Forwards requests to mailhog:8025
# ✅ HEADERS: Security headers and proper proxy configuration
# ===============================================================================

server {
    listen 80;
    server_name _;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Basic Authentication for MailHog
    auth_basic "MailHog Email Interface";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    # Proxy all requests to MailHog
    location / {
        proxy_pass http://mailhog:8025;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support for MailHog real-time updates
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Health check endpoint (bypasses auth for monitoring)
    location /health {
        auth_basic off;
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Deny access to sensitive files
    location ~ /\.(htpasswd|htaccess) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Error pages
    error_page 401 /401.html;
    location = /401.html {
        auth_basic off;
        internal;
        return 401 '<!DOCTYPE html>
<html>
<head>
    <title>Authentication Required</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 400px; margin: 0 auto; padding: 20px; }
        .error { color: #d32f2f; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="error">Authentication Required</h1>
        <p>Please enter valid credentials to access MailHog.</p>
        <p><strong>Username:</strong> admin</p>
        <p><strong>Password:</strong> mailhog123</p>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }
}
