-- Admin Settings Table for storing tracking codes and other admin configurations
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(id)
);

-- SEO Settings Table for managing meta tags and verification codes
CREATE TABLE IF NOT EXISTS seo_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page VARCHAR(100) UNIQUE NOT NULL,
    title_en VARCHAR(255),
    title_ua VARCHAR(255),
    description_en TEXT,
    description_ua TEXT,
    keywords_en TEXT,
    keywords_ua TEXT,
    verification_codes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(id)
);

-- Add access_expiry_date column to users table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'access_expiry_date') THEN
        ALTER TABLE users ADD COLUMN access_expiry_date TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);
CREATE INDEX IF NOT EXISTS idx_seo_settings_page ON seo_settings(page);
CREATE INDEX IF NOT EXISTS idx_users_access_expiry ON users(access_expiry_date);
CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active);

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to admin tables
DROP TRIGGER IF EXISTS update_admin_settings_updated_at ON admin_settings;
CREATE TRIGGER update_admin_settings_updated_at
    BEFORE UPDATE ON admin_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_seo_settings_updated_at ON seo_settings;
CREATE TRIGGER update_seo_settings_updated_at
    BEFORE UPDATE ON seo_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin settings
INSERT INTO admin_settings (key, value, description) VALUES 
('tracking_code', '', 'JavaScript tracking code for analytics (Google Tag Manager, Facebook Pixel, etc.)')
ON CONFLICT (key) DO NOTHING;

-- Insert default SEO settings for main pages
INSERT INTO seo_settings (page, title_en, title_ua, description_en, description_ua, keywords_en, keywords_ua) VALUES 
('home', 'Google Ads Supercharge - Automate Your Campaigns', 'Google Ads Supercharge - Автоматизуйте Ваші Кампанії', 
 'Powerful Google Ads automation tools and scripts to optimize your campaigns, manage budgets, and boost performance.', 
 'Потужні інструменти автоматизації Google Ads та скрипти для оптимізації ваших кампаній, управління бюджетами та підвищення продуктивності.',
 'google ads, automation, scripts, campaign optimization, budget management', 
 'google ads, автоматизація, скрипти, оптимізація кампаній, управління бюджетом'),
('dashboard', 'Dashboard - Google Ads Supercharge', 'Панель Управління - Google Ads Supercharge',
 'Access powerful Google Ads automation tools from your personalized dashboard.', 
 'Отримайте доступ до потужних інструментів автоматизації Google Ads з вашої персоналізованої панелі управління.',
 'dashboard, google ads tools, automation, campaign management', 
 'панель управління, інструменти google ads, автоматизація, управління кампаніями'),
('portfolio', 'Portfolio - Google Ads Expert', 'Портфоліо - Експерт Google Ads',
 'Certified Google Ads specialist with proven track record in campaign optimization and automation.', 
 'Сертифікований спеціаліст Google Ads з підтвердженим досвідом оптимізації кампаній та автоматизації.',
 'google ads expert, portfolio, certification, campaign optimization', 
 'експерт google ads, портфоліо, сертифікація, оптимізація кампаній'),
('careers', 'Careers - Join Our Team', 'Карєра - Приєднуйтесь до Нашої Команди',
 'Join our team and help build the future of Google Ads automation. Open positions for developers, testers, and marketers.',
 'Приєднуйтесь до нашої команди та допоможіть будувати майбутнє автоматизації Google Ads. Відкриті позиції для розробників, тестувальників та маркетологів.',
 'careers, jobs, google ads, developers, marketing, remote work',
 'карєра, робота, google ads, розробники, маркетинг, віддалена робота')
ON CONFLICT (page) DO NOTHING;

-- Grant permissions to gads_user
GRANT ALL PRIVILEGES ON admin_settings TO gads_user;
GRANT ALL PRIVILEGES ON seo_settings TO gads_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO gads_user;
