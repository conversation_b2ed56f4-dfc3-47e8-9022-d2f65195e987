# gAds Supercharge - Docker Compose для Coolify

## Что в docker-compose.yml

### Сервисы:
- **postgres** - База данных PostgreSQL 15
- **redis** - Кеш Redis 7
- **backend** - API сервер (Node.js)
- **frontend** - Фронтенд (React + Vite)
- **mailhog** - Email сервис для тестов
- **postfix** - Email сервис для продакшна
- **nginx** - Reverse proxy (опционально)

## Как использовать

### 2. Настрой переменные окружения в Coolify

**ОБЯЗАТЕЛЬНЫЕ:**
- `POSTGRES_PASSWORD` - пароль для БД (минимум 16 символов)
- `JWT_SECRET` - секрет для JWT токенов (минимум 32 символа)

**ДОПОЛНИТЕЛЬНЫЕ:**
- `POSTGRES_DB=gads_db`
- `POSTGRES_USER=gads_user`
- `FRONTEND_URL=https://gads-supercharge.online`
- `BACKEND_URL=https://api.gads-supercharge.online`

### 3. Домены
- **Frontend:** `gads-supercharge.online`
- **Backend API:** `api.gads-supercharge.online`
- **Mail UI:** `mail.gads-supercharge.online`

### 4. Email сервисы

#### MailHog (для тестов)
- URL: `mail.gads-supercharge.online:8025`
- Перехватывает все мейлы и показывает в веб-интерфейсе
- НЕ отправляет реальные мейлы

#### Postfix (для продакшна)
Добавь переменные для реального SMTP:
```
SMTP_RELAY_HOST=smtp.sendgrid.net
SMTP_USERNAME=apikey
SMTP_PASSWORD=твой_sendgrid_api_key
```

### 5. Структура файлов

Нужны эти файлы в репозитории:
```
/
├── backend/
│   ├── Dockerfile
│   └── database/init.sql
├── Dockerfile.frontend
└── docker-compose.yml
```

### 6. Деплой

1. Вставь docker-compose.yml в Coolify
2. Установи переменные окружения
3. Нажми Deploy
4. Готово!

## Проблемы?

- **Не стартует:** проверь логи в Coolify
- **Нет доступа к БД:** проверь `POSTGRES_PASSWORD`
- **JWT ошибки:** проверь `JWT_SECRET`
- **CORS ошибки:** проверь `FRONTEND_URL` и `BACKEND_URL`

## Порты

- Frontend: 80
- Backend: 3001
- PostgreSQL: 5432
- Redis: 6379
- MailHog: 8025

## Мониторинг

Все сервисы имеют health checks:
- PostgreSQL: `pg_isready`
- Redis: `redis-cli ping`
- Backend: `curl /health`
- Frontend: `curl /health`

## Volumes

Данные сохраняются в:
- `/opt/gads/postgres_data` - данные БД
- `/opt/gads/redis_data` - данные Redis
- `/opt/gads/logs` - логи backend
- `/opt/gads/uploads` - загруженные файлы

## Email настройки

### MailHog (разработка)
- SMTP сервер: `mailhog:1025`
- Веб-интерфейс: `mail.gads-supercharge.online:8025`
- Все мейлы перехватываются и показываются в веб-интерфейсе

### Postfix (продакшн)
Для реальных мейлов добавь:
```
SMTP_RELAY_HOST=smtp.sendgrid.net
SMTP_USERNAME=apikey
SMTP_PASSWORD=SG.xxxxx
```

### Документация email провайдеров
- **SendGrid**: https://docs.sendgrid.com/
- **Mailgun**: https://documentation.mailgun.com/
- **Amazon SES**: https://docs.aws.amazon.com/ses/