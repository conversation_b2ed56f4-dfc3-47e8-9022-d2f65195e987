import { Feature } from '../types';

export const features: Feature[] = [
  {
    id: 1,
    title: 'Powerful Analytics',
    description: 'Get detailed insights and metrics on your advertising campaigns.',
    icon: 'pie-chart',
  },
  {
    id: 2,
    title: 'Smart Automation',
    description: 'Automate your ad placements for optimal performance.',
    icon: 'zap',
  },
  {
    id: 3,
    title: 'Advanced Targeting',
    description: 'Reach the right audience with precision targeting options.',
    icon: 'target',
  },
  {
    id: 4,
    title: 'Comprehensive Reporting',
    description: 'Generate detailed reports to track your ROI and campaign success.',
    icon: 'file-text',
  },
  {
    id: 5,
    title: 'Budget Optimization',
    description: 'Get the most out of your advertising budget with smart allocation.',
    icon: 'piggy-bank',
  },
  {
    id: 6,
    title: 'Multi-platform Support',
    description: 'Manage ads across multiple platforms from a single dashboard.',
    icon: 'layout-grid',
  },
  {
    id: 7,
    title: 'Telegram Ads Script Generator',
    description: 'Generate Google Ads scripts to send daily performance reports to Telegram.',
    icon: 'file-text',
  },
];