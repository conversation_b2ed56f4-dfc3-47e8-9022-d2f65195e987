/**
 * Utility functions for safely handling localStorage operations
 */

/**
 * Safely store a value in localStorage with proper serialization
 * @param key The localStorage key
 * @param value The value to store
 * @param prefix Optional prefix to apply to the key
 */
export const safeLocalStorageSet = (key: string, value: unknown, prefix = ''): void => {
  try {
    const prefixedKey = prefix ? `${prefix}${key}` : key;
    
    // Handle different types appropriately
    if (value === undefined || value === null) {
      localStorage.removeItem(prefixedKey);
      return;
    }
    
    // Store booleans as strings 'true'/'false'
    if (typeof value === 'boolean') {
      localStorage.setItem(prefixedKey, value ? 'true' : 'false');
      return;
    }
    
    // Store numbers and strings directly
    if (typeof value === 'number' || typeof value === 'string') {
      localStorage.setItem(prefixedKey, String(value));
      return;
    }
    
    // For objects and arrays, use JSON serialization
    localStorage.setItem(prefixedKey, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting localStorage key "${key}":`, error);
  }
};

/**
 * Safely retrieve a value from localStorage with proper deserialization
 * @param key The localStorage key
 * @param defaultValue The default value to return if the key doesn't exist or there's an error
 * @param prefix Optional prefix applied to the key
 * @returns The retrieved value or the default value
 */
export const safeLocalStorageGet = <T,>(key: string, defaultValue: T, prefix = ''): T => {
  try {
    const prefixedKey = prefix ? `${prefix}${key}` : key;
    const value = localStorage.getItem(prefixedKey);
    
    if (value === null) {
      return defaultValue;
    }
    
    // Handle boolean values
    if (value === 'true') return true as unknown as T;
    if (value === 'false') return false as unknown as T;
    
    // Handle numeric values
    if (typeof defaultValue === 'number') {
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        return numValue as unknown as T;
      }
    }
    
    // Handle string values
    if (typeof defaultValue === 'string') {
      return value as unknown as T;
    }
    
    // For objects and arrays, use JSON parsing
    return JSON.parse(value) as T;
  } catch (error) {
    console.error(`Error getting localStorage key "${key}":`, error);
    
    // Clean up the problematic value
    try {
      const prefixedKey = prefix ? `${prefix}${key}` : key;
      localStorage.removeItem(prefixedKey);
    } catch {
      // Ignore cleanup errors
    }
    
    return defaultValue;
  }
};

/**
 * Clear all localStorage items with a specific prefix
 * @param prefix The prefix to match
 */
export const clearLocalStorageWithPrefix = (prefix: string): void => {
  try {
    const keysToRemove: string[] = [];
    
    // Find all keys with the prefix
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }
    
    // Remove the keys
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    console.log(`Cleared ${keysToRemove.length} localStorage items with prefix "${prefix}"`);
  } catch (error) {
    console.error(`Error clearing localStorage with prefix "${prefix}":`, error);
  }
};
