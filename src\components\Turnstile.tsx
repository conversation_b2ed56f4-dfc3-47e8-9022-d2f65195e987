import React, { useState } from 'react';

interface SimpleCaptchaProps {
  onVerify: (isValid: boolean) => void;
  className?: string;
}

const SimpleCaptcha: React.FC<SimpleCaptchaProps> = ({
  onVerify,
  className = ''
}) => {
  const [num1] = useState(() => Math.floor(Math.random() * 10) + 1);
  const [num2] = useState(() => Math.floor(Math.random() * 10) + 1);
  const [answer, setAnswer] = useState('');
  const [isVerified, setIsVerified] = useState(false);

  const handleAnswerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAnswer(value);
    
    const correctAnswer = num1 + num2;
    const isCorrect = parseInt(value) === correctAnswer;
    
    if (isCorrect && !isVerified) {
      setIsVerified(true);
      onVerify(true);
    } else if (!isCorrect && isVerified) {
      setIsVerified(false);
      onVerify(false);
    }
  };

  return (
    <div className={`simple-captcha-container ${className}`}>
      <div className="bg-gray-700 border border-gray-600 rounded p-4">
        <div className="flex items-center space-x-3">
          <span className="text-gray-300 text-sm">
            🔒 Security check: {num1} + {num2} = ?
          </span>
          <input
            type="number"
            value={answer}
            onChange={handleAnswerChange}
            className="w-16 px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
            placeholder="?"
          />
          {isVerified && (
            <span className="text-green-400 text-sm">✓</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleCaptcha;