# Nginx configuration for gAds Supercharge production deployment
# Optimized for Coolify v4 with SSL termination

upstream backend {
    server gads-backend:3001;
}

upstream frontend {
    server gads-frontend:80;
}

upstream mailhog {
    server gads-mailhog:8025;
}

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

server {
    listen 80;
    server_name gads-supercharge.online api.gads-supercharge.online mail.gads-supercharge.online;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # API routes (api.gads-supercharge.online)
    location /api/ {
        if ($host != api.gads-supercharge.online) {
            return 404;
        }
        
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://backend/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health endpoint
    location /health {
        if ($host != api.gads-supercharge.online) {
            return 404;
        }
        
        proxy_pass http://backend/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Email UI (mail.gads-supercharge.online)
    location / {
        if ($host = mail.gads-supercharge.online) {
            proxy_pass http://mailhog/;
            break;
        }
        
        # Frontend (gads-supercharge.online)
        if ($host = gads-supercharge.online) {
            limit_req zone=general burst=50 nodelay;
            
            proxy_pass http://frontend/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                proxy_pass http://frontend;
                proxy_set_header Host $host;
                add_header Cache-Control "public, max-age=31536000, immutable";
                expires 1y;
            }
            break;
        }
        
        return 404;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(sql|env|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# SSL server block (handled by Coolify/Cloudflare)
server {
    listen 443 ssl http2;
    server_name gads-supercharge.online api.gads-supercharge.online mail.gads-supercharge.online;
    
    # SSL handled by Coolify + Cloudflare
    
    # Same configuration as port 80
    include /etc/nginx/conf.d/gads-common.conf;
}