-- Create admin_settings table
CREATE TABLE IF NOT EXISTS admin_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID
);

-- Create seo_settings table
CREATE TABLE IF NOT EXISTS seo_settings (
    id SERIAL PRIMARY KEY,
    page VARCHAR(255) UNIQUE NOT NULL,
    title_en VARCHAR(255),
    title_ua VARCHAR(255),
    description_en TEXT,
    description_ua TEXT,
    keywords_en TEXT,
    keywords_ua TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID
);

-- Insert default SEO settings
INSERT INTO seo_settings (page, title_en, title_ua, description_en, description_ua, keywords_en, keywords_ua)
VALUES 
    ('home', 'gAds Supercharge - Google Ads Automation', 'gAds Supercharge - Автоматизація Google Ads', 
     'Professional Google Ads automation and management platform', 'Професійна платформа автоматизації та управління Google Ads',
     'google ads, automation, ppc, advertising', 'google ads, автоматизація, ppc, реклама'),
    ('dashboard', 'Dashboard - gAds Supercharge', 'Панель керування - gAds Supercharge',
     'Manage your Google Ads campaigns and tools', 'Управляйте вашими кампаніями Google Ads та інструментами',
     'dashboard, campaign management, tools', 'панель керування, управління кампаніями, інструменти')
ON CONFLICT (page) DO NOTHING;