# Miscellaneous Files

This directory contains development scripts, test files, and utilities that are not part of the main application but may be useful for development, testing, or maintenance.

## Database Scripts

### User Management
- `create-new-users-2025.cjs` - Script to create new users with 2025 credentials
- `create-new-users.js` - Legacy user creation script
- `create-users.sql` - SQL script for user creation
- `update-users.sql` - SQL script for user updates
- `fix-passwords.js` - Password fixing utility
- `hash-passwords.js` - Password hashing utility

### Translation Management
- `add_tools_translations.cjs` - Script to add tool translations
- `additional_translations.sql` - Additional translation SQL
- `fix_translations.sql` - Translation fixing SQL
- `missing_translations.sql` - Missing translations SQL
- `simple-update.sql` - Simple update SQL script
- `tools_translations_uk.json` - Ukrainian translations JSON

## Test Files

### Authentication Tests
- `test-auth.cjs` - Authentication testing script
- `test-login.cjs` - Login testing script
- `test-login.html` - HTML login test page

### API Tests
- `test-api.html` - API testing HTML page
- `test-fullstack.sh` - Full-stack testing shell script

## Utilities

### Validation
- `script-validator.js` - Script validation utility

## Usage

These files are for development and testing purposes only. They should not be deployed to production environments.

### Running Scripts

Most `.cjs` files can be run with Node.js:
```bash
node misc/script-name.cjs
```

SQL files can be executed against the PostgreSQL database:
```bash
psql -U gads_user -d gads_db -f misc/script-name.sql
```

## Note

These files were moved from the project root to keep the main directory clean and organized. They contain historical development artifacts and testing utilities that may be useful for future development or debugging.
