import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../utils/auth';

// This component redirects already authenticated users to the dashboard
const LoginRedirect: React.FC = () => {
  const { isAuthenticated, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Redirect to dashboard if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  // Otherwise, render the login page
  return <Navigate to="/login" replace />;
};

export default LoginRedirect;
