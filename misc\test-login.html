<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form { max-width: 400px; margin: 0 auto; }
        input, button { width: 100%; padding: 10px; margin: 5px 0; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <div class="form">
        <h1>🔑 Test Login API</h1>
        
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="Admin2025!Secure#">
        <button onclick="testLogin()">Test Login</button>
        
        <div id="result" class="result" style="display:none;"></div>
        
        <h3>Test Accounts:</h3>
        <ul>
            <li><strong>Admin:</strong> <EMAIL> / Admin2025!Secure#</li>
            <li><strong>User:</strong> <EMAIL> / User2025!Strong#</li>
            <li><strong>Demo:</strong> <EMAIL> / Demo2025!Test#</li>
        </ul>
    </div>

    <script>
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const result = document.getElementById('result');
            
            result.style.display = 'block';
            result.innerHTML = 'Testing...';
            result.className = 'result';
            
            try {
                console.log('Testing login with:', email);
                
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok && data.token) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>Token:</strong> ${data.token.substring(0, 50)}...</p>
                        <p><strong>User:</strong> ${data.user.email} (${data.user.role})</p>
                        <p><strong>Message:</strong> ${data.message}</p>
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Error:</strong> ${data.error || data.message}</p>
                        <p><strong>Status:</strong> ${response.status}</p>
                    `;
                }
            } catch (error) {
                console.error('Login error:', error);
                result.className = 'result error';
                result.innerHTML = `
                    <h3>💥 Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Check if backend is running on http://localhost:3001</p>
                `;
            }
        }
    </script>
</body>
</html>
