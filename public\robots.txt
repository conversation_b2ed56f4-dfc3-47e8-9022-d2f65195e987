# https://www.robotstxt.org/robotstxt.html
User-agent: *
Allow: /

# Disallow access to login and client area
Disallow: /login
Disallow: /app/*
Disallow: /dashboard/*
Disallow: /client/*

# Sitemap location
Sitemap: https://gads.example.com/sitemap.xml

# Crawl-delay: 10
# 10 second delay between requests to reduce server load

# Additional disallows (commented out by default)
# Disallow: /private/
# Disallow: /admin/
# Disallow: /api/

# Block common CMS and admin paths
Disallow: /wp-admin/
Disallow: /wp-includes/
Disallow: /wp-login.php
Disallow: /wp-content/plugins/
Disallow: /wp-content/themes/
Disallow: /xmlrpc.php

# Block query strings that might cause duplicate content
Disallow: *?s=
Disallow: *&s=
Disallow: */search/
Disallow: *?search=

# Block all files of these types
Disallow: /*.js$
Disallow: /*.css$
Disallow: /*.php$
Disallow: /*?*
