const bcrypt = require('bcryptjs');

// Test different password hashes that might be in production
const password = 'Admin2025!Secure#';

const possibleHashes = [
  '$2a$12$fJTHWkcQjhO10LFZMzEwyewd0LtU.jW8BoRj83cOsmJz6CYk6nhRS', // Previous hash
  '$2a$12$3N.FwZQ5umy0oNUWsgcw1u/1QxcB8v9Yj7Fu1zvcwIxF/304gk4YS', // New hash
  '$2b$12$fJTHWkcQjhO10LFZMzEwyewd0LtU.jW8BoRj83cOsmJz6CYk6nhRS', // $2b variant
];

console.log('Testing password:', password);
console.log('');

possibleHashes.forEach((hash, index) => {
  const isValid = bcrypt.compareSync(password, hash);
  console.log(`Hash ${index + 1}: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
  console.log(`  ${hash}`);
  console.log('');
});

// Generate a fresh hash
const freshHash = bcrypt.hashSync(password, 12);
console.log('Fresh hash:', freshHash);
console.log('Fresh hash valid:', bcrypt.compareSync(password, freshHash));
