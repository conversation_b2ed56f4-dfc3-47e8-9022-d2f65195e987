// Using built-in fetch (Node 18+)

async function createUsers() {
  const API_URL = 'https://api.gads-supercharge.online';
  
  const users = [
    {
      email: '<EMAIL>',
      password: 'Admin2025!Secure#',
      role: 'admin'
    },
    {
      email: '<EMAIL>', 
      password: 'User2025!Strong#',
      role: 'user'
    },
    {
      email: '<EMAIL>',
      password: 'Demo2025!Test#', 
      role: 'user'
    },
    {
      email: '<EMAIL>',
      password: 'Test2025!Complex#',
      role: 'user'
    }
  ];

  for (const user of users) {
    try {
      console.log(`Creating user: ${user.email}`);
      
      const response = await fetch(`${API_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(user)
      });
      
      if (response.ok) {
        console.log(`✅ User ${user.email} created successfully`);
      } else {
        const error = await response.text();
        console.log(`⚠️ User ${user.email}: ${error}`);
      }
    } catch (error) {
      console.error(`❌ Error creating ${user.email}:`, error.message);
    }
  }
}

createUsers();