import React from 'react';
import Header from '../components/Header';
import Login from '../components/Login';

const LoginPage: React.FC = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow bg-gradient-to-b from-gray-900 to-gray-800 text-white overflow-hidden flex flex-col">
        <div className="relative pt-28 pb-20 md:pt-36 md:pb-28 flex-grow">
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-blue-900/20 to-transparent"></div>
            <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
          </div>
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <Login />
          </div>
        </div>
      </main>
    </div>
  );
};

export default LoginPage;
