<!DOCTYPE html>
<html>
<head>
    <title>Admin API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #fee; color: #c00; }
        .success { background: #efe; color: #060; }
    </style>
</head>
<body>
    <h1>Admin API Test</h1>
    
    <button onclick="testLogin()">Test Login</button>
    <button onclick="testUsers()">Test Users API</button>
    <button onclick="testContent()">Test Content API</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        let token = null;
        
        function log(message, isError = false) {
            const div = document.createElement('div');
            div.className = 'result ' + (isError ? 'error' : 'success');
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }
        
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin2025!Secure#'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    token = data.token;
                    localStorage.setItem('auth_token', token);
                    log('✅ Login successful! Token saved.');
                } else {
                    log('❌ Login failed: ' + response.status, true);
                }
            } catch (error) {
                log('❌ Login error: ' + error.message, true);
            }
        }
        
        async function testUsers() {
            if (!token) {
                token = localStorage.getItem('auth_token');
                if (!token) {
                    log('❌ No token found. Please login first.', true);
                    return;
                }
            }
            
            try {
                const response = await fetch(`${API_BASE}/admin/users`, {
                    headers: { 
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const users = await response.json();
                    log(`✅ Users API successful! Found ${users.length} users.`);
                    log(`First user: ${JSON.stringify(users[0], null, 2)}`);
                } else {
                    log('❌ Users API failed: ' + response.status, true);
                }
            } catch (error) {
                log('❌ Users API error: ' + error.message, true);
            }
        }
        
        async function testContent() {
            if (!token) {
                token = localStorage.getItem('auth_token');
                if (!token) {
                    log('❌ No token found. Please login first.', true);
                    return;
                }
            }
            
            try {
                const response = await fetch(`${API_BASE}/admin/content-keys`, {
                    headers: { 
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const content = await response.json();
                    log(`✅ Content API successful! Found ${content.length} content keys.`);
                    log(`First content: ${JSON.stringify(content[0], null, 2)}`);
                } else {
                    log('❌ Content API failed: ' + response.status, true);
                }
            } catch (error) {
                log('❌ Content API error: ' + error.message, true);
            }
        }
        
        // Auto-load token if exists
        window.onload = function() {
            const savedToken = localStorage.getItem('auth_token');
            if (savedToken) {
                token = savedToken;
                log('📋 Found saved token in localStorage');
            }
        };
    </script>
</body>
</html>