const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const emailService = require('../services/emailService');

const router = express.Router();

// Rate limiting for contact form - more restrictive
const contactLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit each IP to 3 requests per windowMs
  message: {
    error: 'Too many contact form submissions',
    message: 'Please wait 15 minutes before submitting another message'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Contact form submission endpoint
 * POST /api/contact
 */
router.post('/', [
  contactLimiter,
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Zа-яА-ЯіІїЇєЄ\s'-]+$/)
    .withMessage('Name contains invalid characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('subject')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Subject must be between 5 and 200 characters'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Message must be between 10 and 2000 characters')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array(),
        message: 'Please check your input and try again'
      });
    }

    const { name, email, subject, message } = req.body;
    
    // Additional security checks
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:text\/html/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];

    const allText = `${name} ${email} ${subject} ${message}`;
    const hasSuspiciousContent = suspiciousPatterns.some(pattern => pattern.test(allText));

    if (hasSuspiciousContent) {
      console.warn('🚨 Suspicious content detected in contact form:', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        email: email
      });
      
      return res.status(400).json({
        error: 'Invalid content',
        message: 'Your message contains invalid content. Please remove any HTML or script tags.'
      });
    }

    // Log the contact attempt
    console.log('📧 Contact form submission:', {
      name: name,
      email: email,
      subject: subject,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });

    // Send email
    const emailResult = await emailService.sendContactEmail({
      name,
      email,
      subject,
      message
    });

    // Success response
    res.status(200).json({
      success: true,
      message: 'Your message has been sent successfully! We will get back to you soon.',
      messageId: emailResult.messageId
    });

  } catch (error) {
    console.error('❌ Contact form error:', error);
    
    // Don't expose internal errors to client
    res.status(500).json({
      error: 'Internal server error',
      message: 'Sorry, there was a problem sending your message. Please try again later.'
    });
  }
});

/**
 * Test email configuration endpoint (for development)
 * GET /api/contact/test
 */
router.get('/test', async (req, res) => {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  try {
    const isConnected = await emailService.testConnection();
    
    res.json({
      emailService: isConnected ? 'connected' : 'disconnected',
      smtpHost: process.env.SMTP_HOST || 'mailhog',
      smtpPort: process.env.SMTP_PORT || 1025,
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    res.status(500).json({
      error: 'Email service test failed',
      message: error.message
    });
  }
});

module.exports = router;
