# Coolify v4 Service Configuration
# gAds Supercharge Application

version: "3.8"

project:
  name: "gads-supercharge"
  description: "Google Ads Automation Platform"
  
services:
  # Database Service
  postgres:
    type: "postgresql"
    version: "15"
    name: "gads-postgres"
    database: "gads_db"
    username: "gads_user"
    password: "${POSTGRES_PASSWORD}"
    volume: "/var/lib/postgresql/data"
    memory_limit: "1GB"
    cpu_limit: "1"
    
  # Cache Service  
  redis:
    type: "redis"
    version: "7"
    name: "gads-redis"
    volume: "/data"
    memory_limit: "256MB"
    cpu_limit: "0.2"
    
  # Backend API Service
  backend:
    type: "application"
    name: "gads-backend"
    source:
      type: "git"
      repository: "${GIT_REPOSITORY}"
      branch: "main"
      build_command: "cd backend && npm ci --only=production"
      start_command: "cd backend && npm start"
    domains:
      - "api.gads-supercharge.online"
    port: 3001
    memory_limit: "512MB"
    cpu_limit: "0.5"
    environment:
      NODE_ENV: "production"
      PORT: "3001"
      DB_HOST: "gads-postgres"
      DB_PORT: "5432"
      DB_NAME: "${POSTGRES_DB}"
      DB_USER: "${POSTGRES_USER}"
      DB_PASSWORD: "${POSTGRES_PASSWORD}"
      JWT_SECRET: "${JWT_SECRET}"
      CORS_ORIGIN: "${FRONTEND_URL}"
      REDIS_HOST: "gads-redis"
      REDIS_PORT: "6379"
    volumes:
      - "./backend/logs:/app/logs"
    depends_on:
      - "postgres"
      - "redis"
    health_check:
      path: "/health"
      interval: "30s"
      timeout: "10s"
      retries: 3
      
  # Frontend Service
  frontend:
    type: "static"
    name: "gads-frontend"
    source:
      type: "git"
      repository: "${GIT_REPOSITORY}"
      branch: "main"
      build_command: "npm ci && npm run build"
      output_directory: "dist"
    domains:
      - "gads-supercharge.online"
    memory_limit: "128MB"
    cpu_limit: "0.2"
    build_environment:
      VITE_API_URL: "${BACKEND_URL}/api"
      VITE_NODE_ENV: "production"
    depends_on:
      - "backend"
      
  # Email Service (Development)
  mailhog:
    type: "docker"
    image: "mailhog/mailhog:latest"
    name: "gads-mailhog"
    domains:
      - "mail.gads-supercharge.online"
    port: 8025
    memory_limit: "64MB"
    cpu_limit: "0.1"
    environment:
      MH_STORAGE: "maildir"
      MH_MAILDIR_PATH: "/tmp"
    profiles:
      - "development"
      - "staging"

# Environment Variables for Coolify
environment:
  # Generated secrets (set in Coolify UI)
  POSTGRES_PASSWORD:
    type: "secret"
    description: "PostgreSQL database password"
    
  JWT_SECRET:
    type: "secret"
    description: "JWT signing secret"
    
  # Configuration variables
  POSTGRES_DB:
    value: "gads_db"
    
  POSTGRES_USER:
    value: "gads_user"
    
  FRONTEND_URL:
    value: "https://gads-supercharge.online"
    
  BACKEND_URL:
    value: "https://api.gads-supercharge.online"
    
  EMAIL_FROM:
    value: "<EMAIL>"
    
  ADMIN_EMAIL:
    value: "<EMAIL>"

# Network Configuration
networks:
  default:
    name: "gads-network"
    
# Volume Configuration
volumes:
  postgres_data:
    type: "persistent"
    size: "10GB"
    
  redis_data:
    type: "persistent"
    size: "1GB"
    
  backend_logs:
    type: "persistent"
    size: "2GB"

# SSL Configuration
ssl:
  enabled: true
  provider: "letsencrypt"
  email: "<EMAIL>"
  
# Backup Configuration
backups:
  postgres:
    schedule: "0 2 * * *" # Daily at 2 AM
    retention: "30d"
    command: "pg_dump -U gads_user gads_db"
    
# Monitoring
monitoring:
  health_checks: true
  logs: true
  metrics: true
  alerts:
    email: "<EMAIL>"
    
# Deployment Configuration
deployment:
  strategy: "rolling"
  max_unavailable: 1
  auto_deploy: true
  branches:
    - "main"