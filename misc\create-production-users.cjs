const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

// Production database connection
const pool = new Pool({
  user: process.env.DB_USER || 'gads_user',
  host: process.env.DB_HOST || 'postgres',
  database: process.env.DB_NAME || 'gads_db',
  password: process.env.DB_PASSWORD || 'gads_password',
  port: process.env.DB_PORT || 5432,
});

async function createProductionUsers() {
  try {
    console.log('🚀 Creating production users...');
    
    // Create users table if it doesn't exist
    console.log('📋 Creating users table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user',
        preferred_language VARCHAR(10) DEFAULT 'en',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ Users table ready');
    
    // Hash passwords from tmp-logins.txt
    const adminHash = await bcrypt.hash('Admin2025!Secure#', 10);
    const userHash = await bcrypt.hash('User2025!Strong#', 10);
    const demoHash = await bcrypt.hash('Demo2025!Test#', 10);
    const testHash = await bcrypt.hash('Test2025!Complex#', 10);
    
    // Check if admin already exists
    const existingAdmin = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (existingAdmin.rows.length > 0) {
      console.log('✅ Admin user already exists, skipping creation');
      return;
    }
    
    console.log('👤 Creating new users...');
    
    // Create new users with correct passwords
    console.log('👤 Creating admin and user accounts...');
    const adminId = uuidv4();
    const userId = uuidv4();
    const demoId = uuidv4();
    const testId = uuidv4();
    
    await pool.query(`
      INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) 
      VALUES 
        ($1, '<EMAIL>', $2, 'admin', 'en', NOW(), NOW()),
        ($3, '<EMAIL>', $4, 'user', 'en', NOW(), NOW()),
        ($5, '<EMAIL>', $6, 'user', 'en', NOW(), NOW()),
        ($7, '<EMAIL>', $8, 'user', 'en', NOW(), NOW())
    `, [adminId, adminHash, userId, userHash, demoId, demoHash, testId, testHash]);
    
    console.log('✅ Production users created successfully!');
    console.log('📧 Admin: <EMAIL> / Admin2025!Secure#');
    console.log('📧 User: <EMAIL> / User2025!Strong#');
    console.log('📧 Demo: <EMAIL> / Demo2025!Test#');
    console.log('📧 Test: <EMAIL> / Test2025!Complex#');
    
    // Verify admin login
    const testResult = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (testResult.rows.length > 0) {
      const isValidPassword = await bcrypt.compare('Admin2025!Secure#', testResult.rows[0].password_hash);
      console.log('🔐 Admin password test:', isValidPassword ? '✅ PASSED' : '❌ FAILED');
    }
    
    console.log('🎉 Ready for production login!');
    
  } catch (error) {
    console.error('❌ Error creating production users:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', async () => {
  console.log('\n👋 Shutting down...');
  await pool.end();
  process.exit(0);
});

createProductionUsers();