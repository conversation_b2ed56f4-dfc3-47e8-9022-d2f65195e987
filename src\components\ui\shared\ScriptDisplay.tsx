import React, { useState } from 'react';
import { Copy, Check, ArrowLeft } from 'lucide-react';
import StyledButton from './StyledButton'; // Assuming StyledButton is in the same directory

interface ScriptDisplayProps {
  scriptContent: string;
  title?: string;
  language?: string; // e.g., 'javascript' for syntax highlighting if a library is used later
  onBack?: () => void; // Optional callback for back button
}

const ScriptDisplay: React.FC<ScriptDisplayProps> = ({ 
  scriptContent,
  title = 'Generated Script',
  language = 'javascript',
  onBack
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(scriptContent).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  if (!scriptContent) {
    return null; // Don't render if there's no script
  }

  return (
    <div className="mt-6 sm:mt-8">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          {onBack && (
            <StyledButton
              variant="outline"
              size="sm"
              onClick={onBack}
              leftIcon={<ArrowLeft className="h-4 w-4" />}
              themeColor='slate'
            >
              Back to Generator
            </StyledButton>
          )}
          <h3 className="text-lg font-semibold text-gray-200">{title}</h3>
        </div>
        <StyledButton
          variant="outline"
          size="sm"
          onClick={handleCopy}
          leftIcon={copied ? <Check className="h-4 w-4 text-green-400" /> : <Copy className="h-4 w-4" />}
          themeColor='slate'
        >
          {copied ? 'Copied!' : 'Copy Script'}
        </StyledButton>
      </div>
      <pre 
        className={`
          bg-slate-900/90 border border-slate-700 rounded-md p-4 
          text-sm text-gray-200 overflow-x-auto 
          shadow-lg w-full min-h-[200px] max-h-[500px]
        `}
      >
        <code className={`language-${language}`}>
          {scriptContent}
        </code>
      </pre>
    </div>
  );
};

export default ScriptDisplay;
