import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { 
  BarChart3, 
  Search, 
  Users, 
  FileEdit,
  Save,
  AlertCircle,
  CheckCircle,
  Eye,
  Plus,
  Edit,
  Shield,
  ArrowLeft,
  X,
  Check
} from 'lucide-react';

interface User {
  id: string;
  email: string;
  role: 'admin' | 'user';
  is_active: boolean;
  created_at: string;
  last_login_at: string | null;
  access_expiry_date: string | null;
}

interface ContentKey {
  id: string;
  key_name: string;
  category: string;
  translations: {
    en: string;
    ua: string;
  };
}

interface SEOSettings {
  page: string;
  title_en: string;
  title_ua: string;
  description_en: string;
  description_ua: string;
  keywords_en: string;
  keywords_ua: string;
}

const AdminSettings: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  // State for different sections
  const [trackingCode, setTrackingCode] = useState('');
  const [verificationCodes, setVerificationCodes] = useState('');
  const [trackingPreview, setTrackingPreview] = useState(false);
  const [selectedPage, setSelectedPage] = useState('home');
  const [seoSettings, setSeoSettings] = useState<SEOSettings>({
    page: 'home',
    title_en: '',
    title_ua: '',
    description_en: '',
    description_ua: '',
    keywords_en: '',
    keywords_ua: ''
  });
  const [users, setUsers] = useState<User[]>([]);
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    role: 'user' as 'admin' | 'user',
    access_expiry_date: '' as string | null
  });
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [editUserData, setEditUserData] = useState({
    email: '',
    password: '',
    role: 'user' as 'admin' | 'user',
    access_expiry_date: '' as string | null
  });
  const [contentKeys, setContentKeys] = useState<ContentKey[]>([]);
  const [contentFilter, setContentFilter] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [editingContent, setEditingContent] = useState<string | null>(null);
  const [editContentData, setEditContentData] = useState({
    en: '',
    ua: ''
  });

  // API Base URL  
  const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

  // Check admin access
  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 text-center max-w-md">
          <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">{t('admin.access_denied')}</h2>
          <p className="text-gray-400">{t('admin.admin_only')}</p>
        </div>
      </div>
    );
  }

  // Utility functions
  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const fetchData = async (endpoint: string) => {
    const token = localStorage.getItem('auth_token');
    console.log('Fetching data from:', endpoint, 'with token:', !!token);
    
    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    return response.json();
  };

  const postData = async (endpoint: string, data: any) => {
    const token = localStorage.getItem('auth_token');
    console.log('Posting data to:', endpoint, 'with token:', !!token);
    
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    console.log('Response status:', response.status);
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    return response.json();
  };

  const putData = async (endpoint: string, data: any) => {
    const token = localStorage.getItem('auth_token');
    console.log('Putting data to:', endpoint, 'with token:', !!token);
    
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    console.log('Response status:', response.status);
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    return response.json();
  };

  // Determine current section
  const getCurrentSection = () => {
    const path = location.pathname;
    if (path.includes('/settings/tracking')) return 'tracking';
    if (path.includes('/settings/seo')) return 'seo';
    if (path.includes('/settings/users')) return 'users';
    if (path.includes('/settings/content')) return 'content';
    return 'overview';
  };

  const currentSection = getCurrentSection();

  const loadDataForSection = async () => {
    const path = location.pathname;
    try {
      setLoading(true);
      
      if (path.includes('/settings/tracking')) {
        // Load tracking code
        try {
          const trackingData = await fetchData('/admin/tracking-code');
          setTrackingCode(trackingData.code || '');
        } catch (error) {
          console.log('No tracking code set yet');
        }
        // Load verification codes
        try {
          const verificationData = await fetchData('/admin/verification-codes');
          setVerificationCodes(verificationData.codes || '');
        } catch (error) {
          console.log('No verification codes set yet');
        }
      } else if (path.includes('/settings/seo')) {
        // Load SEO settings for home page
        try {
          const seoData = await fetchData('/admin/seo/home');
          setSeoSettings(seoData);
        } catch (error) {
          console.log('No SEO settings for home page yet');
        }
      } else if (path.includes('/settings/users')) {
        // Load users
        console.log('Loading users data...');
        const usersData = await fetchData('/admin/users');
        console.log('Users data loaded:', usersData);
        setUsers(usersData);
      } else if (path.includes('/settings/content')) {
        // Load content keys
        const contentData = await fetchData('/admin/content-keys');
        setContentKeys(contentData);
      }
    } catch (error) {
      showMessage('error', 'Failed to load data');
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load data based on current route
  useEffect(() => {
    loadDataForSection();
  }, [location.pathname]);

  // Load SEO settings when page changes
  useEffect(() => {
    if (currentSection === 'seo' && selectedPage) {
      loadSEOSettingsForPage(selectedPage);
    }
  }, [selectedPage, currentSection]);

  // Section navigation items
  const sections = [
    { 
      id: 'tracking', 
      label: t('admin.tracking_analytics'), 
      icon: BarChart3, 
      href: '/dashboard/settings/tracking',
      description: 'Manage JavaScript tracking codes and analytics' 
    },
    { 
      id: 'seo', 
      label: t('admin.seo_management'), 
      icon: Search, 
      href: '/dashboard/settings/seo',
      description: 'Configure SEO meta tags and verification codes'
    },
    { 
      id: 'users', 
      label: t('admin.user_management'), 
      icon: Users, 
      href: '/dashboard/settings/users',
      description: 'Manage user accounts and permissions'
    },
    { 
      id: 'content', 
      label: t('admin.content_management'), 
      icon: FileEdit, 
      href: '/dashboard/settings/content',
      description: 'Edit translations and content'
    }
  ];

  // Save functions
  const saveTrackingCode = async () => {
    try {
      setLoading(true);
      await postData('/admin/tracking-code', { code: trackingCode });
      showMessage('success', 'Tracking code saved successfully');
    } catch (error) {
      showMessage('error', 'Failed to save tracking code');
    } finally {
      setLoading(false);
    }
  };

  const saveVerificationCodes = async () => {
    try {
      setLoading(true);
      await postData('/admin/verification-codes', { codes: verificationCodes });
      showMessage('success', 'Verification codes saved successfully');
    } catch (error) {
      showMessage('error', 'Failed to save verification codes');
    } finally {
      setLoading(false);
    }
  };

  const saveSEOSettings = async () => {
    try {
      setLoading(true);
      await postData('/admin/seo', seoSettings);
      showMessage('success', 'SEO settings saved successfully');
    } catch (error) {
      showMessage('error', 'Failed to save SEO settings');
    } finally {
      setLoading(false);
    }
  };

  const createUser = async () => {
    try {
      setLoading(true);
      await postData('/admin/users', newUser);
      setNewUser({ email: '', password: '', role: 'user', access_expiry_date: '' });
      const usersData = await fetchData('/admin/users');
      setUsers(usersData);
      showMessage('success', 'User created successfully');
    } catch (error) {
      showMessage('error', 'Failed to create user');
    } finally {
      setLoading(false);
    }
  };

  const toggleUserStatus = async (userId: string, isActive: boolean) => {
    try {
      setLoading(true);
      await putData(`/admin/users/${userId}`, { is_active: !isActive });
      const usersData = await fetchData('/admin/users');
      setUsers(usersData);
      showMessage('success', `User ${!isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      showMessage('error', 'Failed to update user status');
    } finally {
      setLoading(false);
    }
  };

  const startEditUser = (user: User) => {
    setEditingUser(user.id);
    setEditUserData({
      email: user.email,
      password: '', // Don't pre-fill password for security
      role: user.role,
      access_expiry_date: user.access_expiry_date ? user.access_expiry_date.split('T')[0] : ''
    });
  };

  const cancelEditUser = () => {
    setEditingUser(null);
    setEditUserData({
      email: '',
      password: '',
      role: 'user',
      access_expiry_date: ''
    });
  };

  const saveEditUser = async (userId: string) => {
    try {
      setLoading(true);
      const updateData: any = {
        email: editUserData.email,
        role: editUserData.role,
        access_expiry_date: editUserData.access_expiry_date
      };
      
      // Only include password if it's provided
      if (editUserData.password.trim()) {
        updateData.password = editUserData.password;
      }

      await putData(`/admin/users/${userId}`, updateData);
      const usersData = await fetchData('/admin/users');
      setUsers(usersData);
      setEditingUser(null);
      showMessage('success', 'User updated successfully');
    } catch (error) {
      showMessage('error', 'Failed to update user');
    } finally {
      setLoading(false);
    }
  };

  const startEditContent = (item: ContentKey) => {
    setEditingContent(item.id);
    setEditContentData({
      en: item.translations.en,
      ua: item.translations.ua
    });
  };

  const cancelEditContent = () => {
    setEditingContent(null);
    setEditContentData({
      en: '',
      ua: ''
    });
  };

  const saveEditContent = async (keyId: string) => {
    try {
      setLoading(true);
      await putData(`/admin/content/${keyId}`, { translations: editContentData });
      const contentData = await fetchData('/admin/content-keys');
      setContentKeys(contentData);
      setEditingContent(null);
      showMessage('success', 'Content updated successfully');
    } catch (error) {
      showMessage('error', 'Failed to update content');
    } finally {
      setLoading(false);
    }
  };

  const validateTrackingCode = (code: string): boolean => {
    return true;
  };

  const loadSEOSettingsForPage = async (page: string) => {
    try {
      const seoData = await fetchData(`/admin/seo/${page}`);
      setSeoSettings(seoData);
    } catch (error) {
      // Reset form if no settings exist for this page
      setSeoSettings({
        page,
        title_en: '',
        title_ua: '',
        description_en: '',
        description_ua: '',
        keywords_en: '',
        keywords_ua: '',
        verification_codes: ''
      });
    }
  };

  const filteredContentKeys = contentKeys.filter(item => {
    // Only show page content, not dashboard/tool content
    const pageCategories = ['services', 'features', 'pricing', 'contact', 'portfolio', 'about', 'careers', 'legal', 'navigation', 'footer', 'auth', 'common', 'errors', 'validation', 'forms', 'buttons', 'status', 'success', 'messages', 'modals', 'tooltips'];
    const isPageContent = pageCategories.includes(item.category);
    
    const matchesSearch = item.key_name.toLowerCase().includes(contentFilter.toLowerCase()) ||
                         (item.translations.en || '').toLowerCase().includes(contentFilter.toLowerCase()) ||
                         (item.translations.ua || '').toLowerCase().includes(contentFilter.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return isPageContent && matchesSearch && matchesCategory;
  });

  const pageCategories = ['services', 'features', 'pricing', 'contact', 'portfolio', 'about', 'careers', 'legal', 'navigation', 'footer', 'auth', 'common', 'errors', 'validation', 'forms', 'buttons', 'status', 'success', 'messages', 'modals', 'tooltips'];
  const categories = ['all', ...Array.from(new Set(contentKeys.filter(item => pageCategories.includes(item.category)).map(item => item.category)))];
  const pages = [
    { id: 'home', label: 'Home Page' },
    { id: 'dashboard', label: 'Dashboard' },
    { id: 'tools', label: 'Tools' },
    { id: 'features', label: 'Features' },
    { id: 'about', label: 'About' },
    { id: 'contact', label: 'Contact' },
    { id: 'login', label: 'Login' },
    { id: 'register', label: 'Register' },
    { id: 'cookies-policy', label: 'Cookies Policy' }
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="border-b border-gray-800 bg-gray-900">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">
                {t('admin.settings_title')}
              </h1>
              <p className="text-gray-400 mt-1">
                {t('admin.settings_subtitle')}
              </p>
            </div>
            {currentSection !== 'overview' && (
              <Link 
                to="/dashboard/settings"
                className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Overview
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className="max-w-7xl mx-auto px-6 pt-4">
          <div className={`p-4 rounded-lg flex items-center ${
            message.type === 'success' 
              ? 'bg-green-900/50 text-green-400 border border-green-800' 
              : 'bg-red-900/50 text-red-400 border border-red-800'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* Overview - Show section cards */}
        {currentSection === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {sections.map((section) => {
              const Icon = section.icon;
              return (
                <Link
                  key={section.id}
                  to={section.href}
                  className="block bg-gray-800 border border-gray-700 rounded-lg p-6 hover:bg-gray-750 hover:border-gray-600 transition-all group"
                >
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-blue-900/50 rounded-lg border border-blue-800 group-hover:bg-blue-900/70 transition-colors">
                      <Icon className="h-6 w-6 text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">
                        {section.label}
                      </h3>
                      <p className="text-gray-400 text-sm">
                        {section.description}
                      </p>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        )}

        {/* Tracking Section */}
        {currentSection === 'tracking' && (
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">{t('admin.tracking_analytics')}</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  JavaScript Tracking Code
                </label>
                <textarea
                  value={trackingCode}
                  onChange={(e) => setTrackingCode(e.target.value)}
                  rows={10}
                  className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg font-mono text-sm text-gray-100 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  placeholder="<!-- Insert Google Tag Manager, Facebook Pixel, or other tracking codes here -->"
                />
                {trackingCode && !validateTrackingCode(trackingCode) && (
                  <p className="text-red-400 text-sm mt-1">
                    <AlertCircle className="h-4 w-4 inline mr-1" />
                    JavaScript syntax error detected
                  </p>
                )}
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={saveTrackingCode}
                  disabled={loading || (trackingCode && !validateTrackingCode(trackingCode))}
                  className="flex items-center bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Tracking Code
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => setTrackingPreview(!trackingPreview)}
                  className="flex items-center bg-gray-700 hover:bg-gray-600 text-white"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {trackingPreview ? 'Hide' : 'Show'} Preview
                </Button>
              </div>
              {trackingPreview && trackingCode && (
                <div className="mt-4 p-4 bg-gray-900 border border-gray-600 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Preview:</h3>
                  <pre className="text-xs text-gray-400 whitespace-pre-wrap">{trackingCode}</pre>
                </div>
              )}

              {/* Verification Codes Section */}
              <div className="border-t border-gray-700 pt-6 mt-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Verification Codes (Google Search Console, Bing Webmaster Tools, etc.)
                  </label>
                  <textarea
                    value={verificationCodes}
                    onChange={(e) => setVerificationCodes(e.target.value)}
                    rows={6}
                    className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg font-mono text-sm text-gray-100 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    placeholder={`<!-- Google Search Console -->\n<meta name="google-site-verification" content="your-verification-code" />\n\n<!-- Bing Webmaster Tools -->\n<meta name="msvalidate.01" content="your-bing-verification-code" />\n\n<!-- Yandex Webmaster -->\n<meta name="yandex-verification" content="your-yandex-verification-code" />`}
                  />
                  <p className="text-gray-400 text-sm mt-2">
                    Add meta tags for search engine verification (Google, Bing, Yandex, etc.)
                  </p>
                </div>
                <div className="flex space-x-3 mt-4">
                  <Button
                    onClick={saveVerificationCodes}
                    disabled={loading}
                    className="flex items-center bg-green-600 hover:bg-green-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Verification Codes
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* SEO Section */}
        {currentSection === 'seo' && (
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">🔍 SEO Management (Database-driven)</h2>
            

            <div className="mb-6 p-4 bg-blue-900/20 border border-blue-700 rounded-lg">
              <h3 className="text-blue-200 font-semibold mb-3">📋 SEO Management Guide</h3>
              <div className="text-blue-200 text-sm space-y-3">
                <div>
                  <strong>Meta Title</strong> (50-60 characters):
                  <br />📝 Example: "Google Ads Expert Services - Boost Your ROI | gAds Supercharge"
                  <br />🎯 Appears in: Search results title, browser tab, social media shares
                </div>
                <div>
                  <strong>Meta Description</strong> (150-160 characters):
                  <br />📝 Example: "Professional Google Ads management services. Get expert campaign optimization, budget control, and performance tracking to grow your business online."
                  <br />🎯 Appears in: Search results snippet under title
                </div>
                <div>
                  <strong>Keywords</strong> (5-10 keywords):
                  <br />📝 Example: "google ads management, ppc optimization, digital marketing, campaign management"
                  <br />🎯 Used for: Internal SEO tracking and optimization
                </div>
                <div className="mt-3 p-2 bg-yellow-900/30 border border-yellow-600 rounded">
                  <strong>🔍 How to Check:</strong>
                  <br />• View page source (Ctrl+U) and look for &lt;title&gt; and &lt;meta name="description"&gt;
                  <br />• Google search "site:yoursite.com page-name" to see how it appears
                  <br />• Use tools like Google Search Console or SEMrush
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Select Page
                </label>
                <select
                  value={selectedPage}
                  onChange={(e) => setSelectedPage(e.target.value)}
                  className="w-full p-2 bg-gray-900 border border-gray-600 rounded-lg text-gray-100 focus:border-blue-500"
                >
                  {pages.map(page => (
                    <option key={page.id} value={page.id}>{page.label}</option>
                  ))}
                </select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    🇬🇧 Meta Title (English)
                  </label>
                  <Input
                    value={seoSettings.title_en || ''}
                    onChange={(e) => setSeoSettings({...seoSettings, title_en: e.target.value})}
                    placeholder="Page title for search engines (English)"
                    className="bg-gray-900 border-gray-600 text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-yellow-300 mb-2">
                    🇺🇦 Meta Title (Ukrainian) 
                  </label>
                  <Input
                    value={seoSettings.title_ua || ''}
                    onChange={(e) => setSeoSettings({...seoSettings, title_ua: e.target.value})}
                    placeholder="Заголовок сторінки для пошукових систем (Українська)"
                    className="bg-gray-900 border-yellow-600 text-gray-100"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    🇬🇧 Meta Description English ({(seoSettings.description_en || '').length}/160)
                  </label>
                  <textarea
                    value={seoSettings.description_en || ''}
                    onChange={(e) => setSeoSettings({...seoSettings, description_en: e.target.value})}
                    rows={3}
                    maxLength={160}
                    className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg text-gray-100 focus:border-blue-500"
                    placeholder="Brief description for search engine results"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-yellow-300 mb-2">
                    🇺🇦 Meta Description Ukrainian ({(seoSettings.description_ua || '').length}/160)
                  </label>
                  <textarea
                    value={seoSettings.description_ua || ''}
                    onChange={(e) => setSeoSettings({...seoSettings, description_ua: e.target.value})}
                    rows={3}
                    maxLength={160}
                    className="w-full p-3 bg-gray-900 border border-yellow-600 rounded-lg text-gray-100 focus:border-yellow-500"
                    placeholder="Короткий опис для результатів пошукових систем"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    🇬🇧 Keywords (English)
                  </label>
                  <Input
                    value={seoSettings.keywords_en || ''}
                    onChange={(e) => setSeoSettings({...seoSettings, keywords_en: e.target.value})}
                    placeholder="keyword1, keyword2, keyword3"
                    className="bg-gray-900 border-gray-600 text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-yellow-300 mb-2">
                    🇺🇦 Keywords (Ukrainian)
                  </label>
                  <Input
                    value={seoSettings.keywords_ua || ''}
                    onChange={(e) => setSeoSettings({...seoSettings, keywords_ua: e.target.value})}
                    placeholder="ключове_слово1, ключове_слово2, ключове_слово3"
                    className="bg-gray-900 border-yellow-600 text-gray-100"
                  />
                </div>
              </div>
              
              
              <Button
                onClick={saveSEOSettings}
                disabled={loading}
                className="flex items-center bg-blue-600 hover:bg-blue-700"
              >
                <Save className="h-4 w-4 mr-2" />
                Save SEO Settings
              </Button>
            </div>
          </div>
        )}

        {/* Users Section */}
        {currentSection === 'users' && (
          <div className="space-y-6">
            {/* Create New User */}
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 shadow-lg">
              <div className="flex items-center mb-6">
                <h2 className="text-2xl font-semibold text-white flex items-center">
                  <Plus className="h-6 w-6 mr-2 text-blue-400" />
                  Create New User
                </h2>
              </div>
              
              <div className="space-y-6">
                {/* Account Details */}
                <div className="bg-gray-900/50 border border-gray-600 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-green-400" />
                    Account Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <Input
                        value={newUser.email}
                        onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                        placeholder="<EMAIL>"
                        className="bg-gray-900 border-gray-600 text-gray-100"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Password *
                      </label>
                      <Input
                        type="password"
                        value={newUser.password}
                        onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                        placeholder="Secure password"
                        className="bg-gray-900 border-gray-600 text-gray-100"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Permissions & Access */}
                <div className="bg-gray-900/50 border border-gray-600 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center">
                    <Users className="h-5 w-5 mr-2 text-purple-400" />
                    Permissions & Access
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        User Role
                      </label>
                      <select
                        value={newUser.role}
                        onChange={(e) => setNewUser({...newUser, role: e.target.value as 'admin' | 'user'})}
                        className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg text-gray-100 focus:border-blue-500"
                      >
                        <option value="user">👤 User - Standard Access</option>
                        <option value="admin">👑 Admin - Full Access</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Account Expiry
                      </label>
                      <div className="space-y-2">
                        <select
                          value={newUser.access_expiry_date === null ? 'never' : 'custom'}
                          onChange={(e) => {
                            if (e.target.value === 'never') {
                              setNewUser({...newUser, access_expiry_date: null});
                            } else {
                              setNewUser({...newUser, access_expiry_date: ''});
                            }
                          }}
                          className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg text-gray-100 focus:border-blue-500"
                        >
                          <option value="never">♾️ Never Expires</option>
                          <option value="custom">📅 Set Expiry Date</option>
                        </select>
                        {newUser.access_expiry_date !== null && (
                          <Input
                            type="date"
                            value={newUser.access_expiry_date}
                            onChange={(e) => setNewUser({...newUser, access_expiry_date: e.target.value})}
                            className="bg-gray-900 border-gray-600 text-gray-100"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Create Button */}
              <div className="flex justify-end mt-6 pt-4 border-t border-gray-700">
                <Button
                  onClick={createUser}
                  disabled={loading || !newUser.email || !newUser.password}
                  className="flex items-center bg-blue-600 hover:bg-blue-700 px-8 py-3 text-lg"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Create User
                </Button>
              </div>
            </div>

            {/* Edit User Modal */}
            {editingUser && (
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 shadow-xl">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-semibold text-white flex items-center">
                    <Edit className="h-6 w-6 mr-2 text-blue-400" />
                    Edit User
                  </h2>
                </div>
                
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="bg-gray-900/50 border border-gray-600 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center">
                      <Shield className="h-5 w-5 mr-2 text-green-400" />
                      Account Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Email Address
                        </label>
                        <Input
                          value={editUserData.email}
                          onChange={(e) => setEditUserData({...editUserData, email: e.target.value})}
                          placeholder="<EMAIL>"
                          className="bg-gray-900 border-gray-600 text-gray-100"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          New Password (optional)
                        </label>
                        <Input
                          type="password"
                          value={editUserData.password}
                          onChange={(e) => setEditUserData({...editUserData, password: e.target.value})}
                          placeholder="Leave empty to keep current password"
                          className="bg-gray-900 border-gray-600 text-gray-100"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Permissions & Access */}
                  <div className="bg-gray-900/50 border border-gray-600 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center">
                      <Users className="h-5 w-5 mr-2 text-purple-400" />
                      Permissions & Access
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          User Role
                        </label>
                        <select
                          value={editUserData.role}
                          onChange={(e) => setEditUserData({...editUserData, role: e.target.value as 'admin' | 'user'})}
                          className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg text-gray-100 focus:border-blue-500"
                        >
                          <option value="user">👤 User - Standard Access</option>
                          <option value="admin">👑 Admin - Full Access</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Account Expiry
                        </label>
                        <div className="space-y-2">
                          <select
                            value={editUserData.access_expiry_date === null ? 'never' : 'custom'}
                            onChange={(e) => {
                              if (e.target.value === 'never') {
                                setEditUserData({...editUserData, access_expiry_date: null});
                              } else {
                                setEditUserData({...editUserData, access_expiry_date: ''});
                              }
                            }}
                            className="w-full p-3 bg-gray-900 border border-gray-600 rounded-lg text-gray-100 focus:border-blue-500"
                          >
                            <option value="never">♾️ Never Expires</option>
                            <option value="custom">📅 Set Expiry Date</option>
                          </select>
                          {editUserData.access_expiry_date !== null && (
                            <Input
                              type="date"
                              value={editUserData.access_expiry_date}
                              onChange={(e) => setEditUserData({...editUserData, access_expiry_date: e.target.value})}
                              className="bg-gray-900 border-gray-600 text-gray-100"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-700">
                  <Button
                    onClick={cancelEditUser}
                    variant="secondary"
                    className="flex items-center bg-gray-700 hover:bg-gray-600 text-white px-6"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    onClick={() => saveEditUser(editingUser)}
                    disabled={loading || !editUserData.email}
                    className="flex items-center bg-green-600 hover:bg-green-700 px-6"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </div>
              </div>
            )}

            {/* Users Table */}
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Existing Users</h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-3 text-gray-300 font-medium">Email</th>
                      <th className="text-left p-3 text-gray-300 font-medium">Role</th>
                      <th className="text-left p-3 text-gray-300 font-medium">Status</th>
                      <th className="text-left p-3 text-gray-300 font-medium">Created</th>
                      <th className="text-left p-3 text-gray-300 font-medium">Last Login</th>
                      <th className="text-left p-3 text-gray-300 font-medium">Expires</th>
                      <th className="text-left p-3 text-gray-300 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map(user => (
                      <tr key={user.id} className="border-b border-gray-700 hover:bg-gray-750">
                        {editingUser === user.id ? (
                          // Editing row
                          <>
                            <td className="p-3">
                              <Input
                                value={editUserData.email}
                                onChange={(e) => setEditUserData({...editUserData, email: e.target.value})}
                                className="bg-gray-900 border-gray-600 text-gray-100 text-sm"
                              />
                            </td>
                            <td className="p-3">
                              <select
                                value={editUserData.role}
                                onChange={(e) => setEditUserData({...editUserData, role: e.target.value as 'admin' | 'user'})}
                                className="p-1 bg-gray-900 border border-gray-600 rounded text-gray-100 text-sm"
                              >
                                <option value="user">User</option>
                                <option value="admin">Admin</option>
                              </select>
                            </td>
                            <td className="p-3">
                              <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                                user.is_active 
                                  ? 'bg-green-900/50 text-green-400 border border-green-800' 
                                  : 'bg-red-900/50 text-red-400 border border-red-800'
                              }`}>
                                {user.is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="p-3 text-gray-400 text-sm">
                              {new Date(user.created_at).toLocaleDateString()}
                            </td>
                            <td className="p-3 text-gray-400 text-sm">
                              {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : 'Never'}
                            </td>
                            <td className="p-3">
                              <Input
                                type="date"
                                value={editUserData.access_expiry_date}
                                onChange={(e) => setEditUserData({...editUserData, access_expiry_date: e.target.value})}
                                className="bg-gray-900 border-gray-600 text-gray-100 text-sm"
                              />
                            </td>
                            <td className="p-3">
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  onClick={() => saveEditUser(user.id)}
                                  disabled={loading}
                                  className="bg-green-600 hover:bg-green-700 px-2"
                                >
                                  <Check className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  onClick={cancelEditUser}
                                  className="bg-gray-700 hover:bg-gray-600 px-2"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </td>
                          </>
                        ) : (
                          // Normal row
                          <>
                            <td className="p-3 text-gray-100">{user.email}</td>
                            <td className="p-3">
                              <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                                user.role === 'admin' 
                                  ? 'bg-purple-900/50 text-purple-400 border border-purple-800' 
                                  : 'bg-blue-900/50 text-blue-400 border border-blue-800'
                              }`}>
                                {user.role}
                              </span>
                            </td>
                            <td className="p-3">
                              <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                                user.is_active 
                                  ? 'bg-green-900/50 text-green-400 border border-green-800' 
                                  : 'bg-red-900/50 text-red-400 border border-red-800'
                              }`}>
                                {user.is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="p-3 text-gray-400 text-sm">
                              {new Date(user.created_at).toLocaleDateString()}
                            </td>
                            <td className="p-3 text-gray-400 text-sm">
                              {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : 'Never'}
                            </td>
                            <td className="p-3 text-gray-400 text-sm">
                              {user.access_expiry_date ? new Date(user.access_expiry_date).toLocaleDateString() : 'Never'}
                            </td>
                            <td className="p-3">
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  onClick={() => startEditUser(user)}
                                  disabled={loading}
                                  className="bg-blue-600 hover:bg-blue-700 px-2"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant={user.is_active ? "secondary" : "primary"}
                                  onClick={() => toggleUserStatus(user.id, user.is_active)}
                                  disabled={loading}
                                  className={user.is_active ? "bg-gray-700 hover:bg-gray-600 text-white px-2" : "bg-green-600 hover:bg-green-700 px-2"}
                                >
                                  {user.is_active ? 'Deactivate' : 'Activate'}
                                </Button>
                              </div>
                            </td>
                          </>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Content Section */}
        {currentSection === 'content' && (
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">{t('admin.content_management')}</h2>
            
            {/* Content Management Interface */}
            <div className="mb-6">
              <button
                onClick={async () => {
                  try {
                    const response = await fetch('/api/content/auto-populate', {
                      method: 'POST',
                      headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json'
                      }
                    });
                    if (response.ok) {
                      alert('✅ Базовий контент додано успішно!');
                      loadContent();
                    } else {
                      alert('❌ Помилка при додаванні контенту');
                    }
                  } catch (error) {
                    alert('❌ Помилка: ' + error.message);
                  }
                }}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                ✨ Додати Базовий Контент
              </button>
              
              <button
                onClick={async () => {
                  try {
                    const response = await fetch('/api/content/check-missing', {
                      headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                      }
                    });
                    const data = await response.json();
                    if (response.ok) {
                      const missing = data.data;
                      alert(`📊 Статистика:\n❌ Відсутні UA переклади: ${missing.missingUA.length}\n❌ Відсутні EN переклади: ${missing.missingEN.length}`);
                    } else {
                      alert('❌ Помилка при перевірці переводів');
                    }
                  } catch (error) {
                    alert('❌ Помилка: ' + error.message);
                  }
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                📊 Перевірити Відсутні
              </button>

              <button
                onClick={() => {
                  window.open('https://mail.gads-supercharge.online:8025', '_blank');
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                📧 MailHog (Email Test)
              </button>
            </div>

            {/* Help Guide */}
            <div className="mb-6 p-4 bg-green-900/20 border border-green-700 rounded-lg">
              <h3 className="text-green-200 font-semibold mb-3">📝 Content Management Guide</h3>
              <div className="text-green-200 text-sm space-y-2">
                <div><strong>🏠 Page Content:</strong> services.title, features.description, about.subtitle, contact.* - контент зовнішніх сторінок</div>
                <div><strong>🎛️ Dashboard:</strong> dashboard.*, tools.* - тексти інструментів і панелі управління</div>
                <div><strong>🔘 UI Elements:</strong> buttons.*, forms.*, navigation.* - кнопки, форми, меню</div>
                <div><strong>💬 Messages:</strong> errors.*, success.*, validation.* - повідомлення про помилки та успіх</div>
                <div className="mt-3 p-2 bg-yellow-900/30 border border-yellow-600 rounded">
                  <strong>🔍 Як знайти:</strong> Введіть назву сторінки (services, about) або тип контенту (title, description) у пошук
                </div>
              </div>
            </div>
            
            {/* Search and Filter */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Search Content
                </label>
                <Input
                  value={contentFilter}
                  onChange={(e) => setContentFilter(e.target.value)}
                  placeholder="Search by key or translation text..."
                  className="bg-gray-900 border-gray-600 text-gray-100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Filter by Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 bg-gray-900 border border-gray-600 rounded-lg text-gray-100"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Content Keys Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left p-3 text-gray-300 font-medium">Key</th>
                    <th className="text-left p-3 text-gray-300 font-medium">Category</th>
                    <th className="text-left p-3 text-gray-300 font-medium">English</th>
                    <th className="text-left p-3 text-gray-300 font-medium">Ukrainian</th>
                    <th className="text-left p-3 text-gray-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredContentKeys.slice(0, 50).map(item => (
                    <tr key={item.id} className="border-b border-gray-700 hover:bg-gray-750">
                      <td className="p-3 text-blue-400 font-mono text-sm">{item.key_name}</td>
                      <td className="p-3">
                        <span className="inline-flex px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded-full border border-gray-600">
                          {item.category}
                        </span>
                      </td>
                      {editingContent === item.id ? (
                        <>
                          <td className="p-3">
                            <Input
                              value={editContentData.en}
                              onChange={(e) => setEditContentData({...editContentData, en: e.target.value})}
                              className="bg-gray-900 border-gray-600 text-gray-100 text-sm"
                              placeholder="English translation"
                            />
                          </td>
                          <td className="p-3">
                            <Input
                              value={editContentData.ua}
                              onChange={(e) => setEditContentData({...editContentData, ua: e.target.value})}
                              className="bg-gray-900 border-gray-600 text-gray-100 text-sm"
                              placeholder="Ukrainian translation"
                            />
                          </td>
                          <td className="p-3">
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                onClick={() => saveEditContent(item.id)}
                                disabled={loading}
                                className="bg-green-600 hover:bg-green-700 px-2"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={cancelEditContent}
                                className="bg-gray-700 hover:bg-gray-600 px-2"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </>
                      ) : (
                        <>
                          <td className="p-3 text-gray-300 text-sm max-w-xs truncate">{item.translations.en}</td>
                          <td className="p-3 text-gray-300 text-sm max-w-xs truncate">{item.translations.ua}</td>
                          <td className="p-3">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => startEditContent(item)}
                              className="flex items-center bg-gray-700 hover:bg-gray-600 text-white"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                          </td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredContentKeys.length > 50 && (
              <p className="text-gray-400 text-sm mt-4">
                Showing first 50 results. Use search/filter to narrow down.
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSettings;