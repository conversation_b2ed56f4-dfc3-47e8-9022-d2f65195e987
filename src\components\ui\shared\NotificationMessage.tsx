import React, { ReactNode } from 'react';
import { AlertTriangle, CheckCircle2, Info, X } from 'lucide-react';

interface NotificationMessageProps {
  type: 'success' | 'error' | 'info' | 'warning';
  message: ReactNode;
  title?: string;
  onDismiss?: () => void;
  className?: string;
}

const notificationThemes = {
  success: {
    bg: 'bg-green-900/80 border-green-700/90',
    iconColor: 'text-green-300',
    titleColor: 'text-green-200',
    textColor: 'text-green-200',
    IconComponent: CheckCircle2,
  },
  error: {
    bg: 'bg-red-900/80 border-red-700/90',
    iconColor: 'text-red-300',
    titleColor: 'text-red-200',
    textColor: 'text-red-200',
    IconComponent: AlertTriangle, // Using AlertTriangle for broader error/critical indication
  },
  info: {
    bg: 'bg-sky-900/80 border-sky-700/90',
    iconColor: 'text-sky-300',
    titleColor: 'text-sky-200',
    textColor: 'text-sky-200',
    IconComponent: Info,
  },
  warning: {
    bg: 'bg-amber-800/80 border-amber-600/90',
    iconColor: 'text-amber-200',
    titleColor: 'text-amber-100',
    textColor: 'text-amber-100',
    IconComponent: AlertTriangle,
  },
};

const NotificationMessage: React.FC<NotificationMessageProps> = ({
  type,
  message,
  title,
  onDismiss,
  className = '',
}) => {
  const theme = notificationThemes[type] || notificationThemes.info;
  const Icon = theme.IconComponent;

  if (!message) return null;

  return (
    <div 
      className={`p-4 rounded-lg border shadow-md flex ${theme.bg} ${className}`}
      role={type === 'error' ? 'alert' : 'status'}
    >
      <div className={`flex-shrink-0 ${theme.iconColor} mr-3 mt-0.5`}>
        <Icon className="h-5 w-5" />
      </div>
      <div className="flex-grow">
        {title && (
          <h3 className={`text-sm font-semibold ${theme.titleColor} mb-1`}>{title}</h3>
        )}
        <div className={`text-sm ${theme.textColor}`}>{message}</div>
      </div>
      {onDismiss && (
        <button 
          onClick={onDismiss}
          className={`ml-3 p-1 rounded-md ${theme.textColor} hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/50`}
          aria-label="Dismiss notification"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default NotificationMessage;
