import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useApiAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import Button from './ui/Button';
import SeasonalBudgetSummary from './dashboard/SeasonalBudgetSummary';

const Dashboard: React.FC = () => {
  const { user, logout } = useApiAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <section className="py-12 md:py-20">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-3xl font-bold text-primary-900">{t('dashboard.title')}</h1>
              <Button
                variant="outline"
                className="text-primary-700 border-primary-700"
                onClick={handleLogout}
              >
                {t('auth.logout')}
              </Button>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-primary-900 mb-4">{t('dashboard.welcomeBack')}, {user?.email}</h2>
              <p className="text-gray-700">
                {t('dashboard.description')}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-primary-900 mb-3">{t('dashboard.userInfo')}</h3>
                <ul className="space-y-2 text-gray-700">
                  <li><span className="font-medium">{t('auth.email')}:</span> {user?.email}</li>
                  <li><span className="font-medium">{t('auth.role')}:</span> {user?.role}</li>
                  <li><span className="font-medium">{t('auth.status')}:</span> <span className="text-green-600">{t('auth.active')}</span></li>
                </ul>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-primary-900 mb-3">{t('dashboard.quickActions')}</h3>
                <div className="space-y-3">
                  <Button variant="secondary" className="w-full justify-center">{t('dashboard.analytics')}</Button>
                  <Button variant="outline" className="w-full justify-center">{t('tools.campaign.title')}</Button>
                  <Button variant="outline" className="w-full justify-center">{t('nav.settings')}</Button>
                </div>
              </div>
            </div>
            
            {/* Tools Section */}
            <div className="mt-8">
              <h2 className="text-2xl font-bold text-primary-900 mb-4">{t('dashboard.toolsSection')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                
                {/* Device Bid Adjuster Tool Card */}
                <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-primary-900">{t('tools.deviceBid.title')}</h3>
                  </div>
                  <p className="text-gray-600 mb-4">{t('tools.deviceBid.description')}</p>
                  <Button 
                    variant="outline" 
                    className="w-full justify-center text-green-600 border-green-600 hover:bg-green-50"
                    onClick={() => navigate('/dashboard/device-bid')}
                  >
                    {t('dashboard.openTool')}
                  </Button>
                </div>
                
                {/* Budget Monitor Tool Card */}
                <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-primary-900">{t('tools.budgetMonitor.title')}</h3>
                  </div>
                  <p className="text-gray-600 mb-4">{t('tools.budgetMonitor.description')}</p>
                  <Button 
                    variant="outline" 
                    className="w-full justify-center text-purple-600 border-purple-600 hover:bg-purple-50"
                    onClick={() => navigate('/dashboard/budget-monitor')}
                  >
                    {t('dashboard.openTool')}
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Recent Adjustments Section */}
            <div className="mt-8">
              <h2 className="text-2xl font-bold text-primary-900 mb-4">{t('dashboard.recentAdjustments')}</h2>
              <SeasonalBudgetSummary />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Dashboard;
