worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    sendfile on;
    keepalive_timeout 65;

    # Define upstream for backend service
    upstream backend_service {
        server gads-backend-container:3001;
    }

    # Frontend service disabled for now

    server {
        listen 80;
        server_name gads-supercharge.online www.gads-supercharge.online;

        # Log files
        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;

        # Proxy API requests to the backend service
        location /api/ {
            proxy_pass http://backend_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Serve static content or redirect to frontend
        location / {
            return 200 'API is working. Frontend will be available soon.';
            add_header Content-Type text/plain;
        }
    }
}
