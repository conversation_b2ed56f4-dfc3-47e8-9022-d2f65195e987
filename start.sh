#!/bin/bash

# gAds Supercharge - Full-Stack Startup Script
# This script helps you start the full-stack application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    print_success "Docker Compose is available"
}

# Function to create environment file if it doesn't exist
setup_env() {
    if [ ! -f "backend/.env" ]; then
        print_warning "Backend .env file not found. Creating from example..."
        cp backend/.env.example backend/.env
        print_success "Created backend/.env from example"
        print_warning "Please review and update backend/.env with your configuration"
    else
        print_success "Backend .env file exists"
    fi
}

# Function to build and start services
start_services() {
    print_status "Building and starting services..."
    
    # Pull latest images
    print_status "Pulling latest base images..."
    docker-compose pull
    
    # Build services
    print_status "Building services..."
    docker-compose build
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    print_success "Services started successfully!"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for PostgreSQL..."
    timeout=60
    while ! docker-compose exec -T postgres pg_isready -U gads_user -d gads_db > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "PostgreSQL failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "PostgreSQL is ready"
    
    # Wait for backend
    print_status "Waiting for backend API..."
    timeout=60
    while ! curl -f http://localhost:3001/health > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Backend API failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Backend API is ready"
    
    # Wait for frontend
    print_status "Waiting for frontend..."
    timeout=60
    while ! curl -f http://localhost:5173/ > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Frontend failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Frontend is ready"
}

# Function to show service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    
    echo ""
    print_status "Service URLs:"
    echo "  Frontend:  http://localhost:5173"
    echo "  Backend:   http://localhost:3001"
    echo "  Database:  localhost:5432"
    
    echo ""
    print_status "Default Login Credentials:"
    echo "  Admin:     <EMAIL> / admin123"
    echo "  User:      <EMAIL> / user123"
}

# Function to show logs
show_logs() {
    print_status "Showing service logs (Ctrl+C to exit)..."
    docker-compose logs -f
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    docker-compose down
    print_success "Services stopped"
}

# Function to clean up
cleanup() {
    print_status "Cleaning up..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed"
}

# Main menu
show_menu() {
    echo ""
    echo "=== gAds Supercharge - Full-Stack Manager ==="
    echo "1. Start services"
    echo "2. Stop services"
    echo "3. Restart services"
    echo "4. Show status"
    echo "5. Show logs"
    echo "6. Clean up"
    echo "7. Exit"
    echo ""
}

# Main script
main() {
    echo "🚀 gAds Supercharge - Full-Stack Application"
    echo "============================================="
    
    # Check prerequisites
    check_docker
    check_docker_compose
    setup_env
    
    if [ $# -eq 0 ]; then
        # Interactive mode
        while true; do
            show_menu
            read -p "Choose an option (1-7): " choice
            
            case $choice in
                1)
                    start_services
                    wait_for_services
                    show_status
                    ;;
                2)
                    stop_services
                    ;;
                3)
                    stop_services
                    start_services
                    wait_for_services
                    show_status
                    ;;
                4)
                    show_status
                    ;;
                5)
                    show_logs
                    ;;
                6)
                    cleanup
                    ;;
                7)
                    print_success "Goodbye!"
                    exit 0
                    ;;
                *)
                    print_error "Invalid option. Please choose 1-7."
                    ;;
            esac
        done
    else
        # Command line mode
        case $1 in
            start)
                start_services
                wait_for_services
                show_status
                ;;
            stop)
                stop_services
                ;;
            restart)
                stop_services
                start_services
                wait_for_services
                show_status
                ;;
            status)
                show_status
                ;;
            logs)
                show_logs
                ;;
            cleanup)
                cleanup
                ;;
            *)
                echo "Usage: $0 [start|stop|restart|status|logs|cleanup]"
                exit 1
                ;;
        esac
    fi
}

# Run main function
main "$@"
