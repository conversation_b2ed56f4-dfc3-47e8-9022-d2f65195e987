-- Missing translation keys for tools
INSERT INTO content_keys (key_name, category, description) VALUES
-- Script Generator Tool
('tools.scriptGenerator.title', 'tools', 'Script Generator tool title'),
('tools.scriptGenerator.description', 'tools', 'Script Generator tool description'),
('tools.scriptGenerator.howItWorks.step1', 'tools', 'Script Generator how it works step 1'),
('tools.scriptGenerator.howItWorks.step2', 'tools', 'Script Generator how it works step 2'), 
('tools.scriptGenerator.howItWorks.step3', 'tools', 'Script Generator how it works step 3'),
('tools.scriptGenerator.howItWorks.step4', 'tools', 'Script Generator how it works step 4'),
('tools.scriptGenerator.howItWorks.step5', 'tools', 'Script Generator how it works step 5'),
('tools.scriptGenerator.sections.campaignDetails', 'tools', 'Campaign details section'),
('tools.scriptGenerator.sections.adContent', 'tools', 'Ad content section'),
('tools.scriptGenerator.sections.keywords', 'tools', 'Keywords section'),
('tools.scriptGenerator.sections.telegram', 'tools', 'Telegram notifications section'),
('tools.scriptGenerator.fields.finalUrl', 'tools', 'Final URL field'),
('tools.scriptGenerator.fields.campaignName', 'tools', 'Campaign name field'),
('tools.scriptGenerator.fields.adGroupName', 'tools', 'Ad group name field'),
('tools.scriptGenerator.fields.dailyBudget', 'tools', 'Daily budget field'),
('tools.scriptGenerator.fields.headlines', 'tools', 'Headlines field'),
('tools.scriptGenerator.fields.descriptions', 'tools', 'Descriptions field'),
('tools.scriptGenerator.fields.keyword', 'tools', 'Keyword field'),
('tools.scriptGenerator.fields.enableTelegram', 'tools', 'Enable Telegram field'),
('tools.scriptGenerator.fields.telegramBotToken', 'tools', 'Telegram bot token field'),
('tools.scriptGenerator.fields.telegramChatId', 'tools', 'Telegram chat ID field'),
('tools.scriptGenerator.placeholders.headline', 'tools', 'Headline placeholder'),
('tools.scriptGenerator.placeholders.description', 'tools', 'Description placeholder'),
('tools.scriptGenerator.placeholders.keyword', 'tools', 'Keyword placeholder'),
('tools.scriptGenerator.tooltips.finalUrl', 'tools', 'Final URL tooltip'),
('tools.scriptGenerator.tooltips.campaignName', 'tools', 'Campaign name tooltip'),
('tools.scriptGenerator.tooltips.adGroupName', 'tools', 'Ad group name tooltip'),
('tools.scriptGenerator.tooltips.dailyBudget', 'tools', 'Daily budget tooltip'),
('tools.scriptGenerator.tooltips.removeHeadline', 'tools', 'Remove headline tooltip'),
('tools.scriptGenerator.tooltips.removeDescription', 'tools', 'Remove description tooltip'),
('tools.scriptGenerator.tooltips.removeKeyword', 'tools', 'Remove keyword tooltip'),
('tools.scriptGenerator.tooltips.telegramBotToken', 'tools', 'Telegram bot token tooltip'),
('tools.scriptGenerator.tooltips.telegramChatId', 'tools', 'Telegram chat ID tooltip'),
('tools.scriptGenerator.buttons.addHeadline', 'tools', 'Add headline button'),
('tools.scriptGenerator.buttons.addDescription', 'tools', 'Add description button'),
('tools.scriptGenerator.buttons.addKeyword', 'tools', 'Add keyword button'),
('tools.scriptGenerator.buttons.generateScript', 'tools', 'Generate script button'),
('tools.scriptGenerator.results.title', 'tools', 'Generated script title'),
('tools.scriptGenerator.errors.requiredFields', 'tools', 'Required fields error'),
('tools.scriptGenerator.errors.invalidBudget', 'tools', 'Invalid budget error'),
('tools.scriptGenerator.errors.invalidUrl', 'tools', 'Invalid URL error'),
('tools.scriptGenerator.errors.telegramRequired', 'tools', 'Telegram required error'),
('tools.scriptGenerator.errors.noAds', 'tools', 'No ads error'),
('tools.scriptGenerator.errors.incompleteAds', 'tools', 'Incomplete ads error'),
('tools.scriptGenerator.errors.noKeywords', 'tools', 'No keywords error'),
('tools.scriptGenerator.success.generated', 'tools', 'Script generated success'),

-- Budget Monitor Tool
('tools.budgetMonitor.title', 'tools', 'Budget Monitor tool title'),
('tools.budgetMonitor.description', 'tools', 'Budget Monitor tool description'),
('tools.budgetMonitor.generated', 'tools', 'Generated budget monitor script'),
('tools.budgetMonitor.howItWorks', 'tools', 'How budget monitor works'),
('tools.budgetMonitor.howItWorks.step1', 'tools', 'Budget monitor step 1'),
('tools.budgetMonitor.howItWorks.step2', 'tools', 'Budget monitor step 2'),
('tools.budgetMonitor.howItWorks.step3', 'tools', 'Budget monitor step 3'),
('tools.budgetMonitor.howItWorks.step3a', 'tools', 'Budget monitor step 3a'),
('tools.budgetMonitor.howItWorks.step3b', 'tools', 'Budget monitor step 3b'),
('tools.budgetMonitor.howItWorks.step4', 'tools', 'Budget monitor step 4'),
('tools.budgetMonitor.howItWorks.step5', 'tools', 'Budget monitor step 5'),
('tools.budgetMonitor.howItWorks.step6', 'tools', 'Budget monitor step 6'),
('tools.budgetMonitor.howItWorks.important', 'tools', 'Budget monitor important note'),
('tools.budgetMonitor.sections.campaigns', 'tools', 'Campaigns section'),
('tools.budgetMonitor.sections.thresholds', 'tools', 'Thresholds section'),
('tools.budgetMonitor.sections.notifications', 'tools', 'Notifications section'),
('tools.budgetMonitor.fields.campaignNames', 'tools', 'Campaign names field'),
('tools.budgetMonitor.fields.highThreshold', 'tools', 'High threshold field'),
('tools.budgetMonitor.fields.lowThreshold', 'tools', 'Low threshold field'),
('tools.budgetMonitor.fields.telegramToken', 'tools', 'Telegram token field'),
('tools.budgetMonitor.fields.telegramChatId', 'tools', 'Telegram chat ID field'),
('tools.budgetMonitor.errors.noCampaigns', 'tools', 'No campaigns error'),
('tools.budgetMonitor.errors.telegramRequired', 'tools', 'Telegram required error'),
('tools.budgetMonitor.errors.invalidThresholds', 'tools', 'Invalid thresholds error'),
('tools.budgetMonitor.errors.thresholdOrder', 'tools', 'Threshold order error'),

-- Performance Max Tool
('tools.performanceMax.title', 'tools', 'Performance Max tool title'),
('tools.performanceMax.description', 'tools', 'Performance Max tool description'),
('tools.performanceMax.sections.campaign', 'tools', 'Campaign section'),
('tools.performanceMax.sections.notifications', 'tools', 'Notifications section'),
('tools.performanceMax.sections.telegramDetails', 'tools', 'Telegram details section'),
('tools.performanceMax.fields.campaignPattern', 'tools', 'Campaign pattern field'),
('tools.performanceMax.fields.dateRange', 'tools', 'Date range field'),
('tools.performanceMax.fields.emailAddress', 'tools', 'Email address field'),
('tools.performanceMax.fields.telegramSummary', 'tools', 'Telegram summary field'),
('tools.performanceMax.fields.telegramToken', 'tools', 'Telegram token field'),
('tools.performanceMax.fields.telegramChatId', 'tools', 'Telegram chat ID field'),
('tools.performanceMax.placeholders.campaignPattern', 'tools', 'Campaign pattern placeholder'),
('tools.performanceMax.placeholders.telegramToken', 'tools', 'Telegram token placeholder'),
('tools.performanceMax.placeholders.telegramChatId', 'tools', 'Telegram chat ID placeholder'),
('tools.performanceMax.errors.campaignRequired', 'tools', 'Campaign required error'),
('tools.performanceMax.errors.invalidEmail', 'tools', 'Invalid email error'),
('tools.performanceMax.errors.telegramRequired', 'tools', 'Telegram required error'),

-- Device Bid Tool  
('tools.deviceBid.title', 'tools', 'Device Bid tool title'),
('tools.deviceBid.description', 'tools', 'Device Bid tool description'),

-- Common keys
('common.backToForm', 'common', 'Back to form button'),
('common.generateScript', 'common', 'Generate script button'),
('common.enableTelegram', 'common', 'Enable Telegram notifications'),
('common.generating', 'common', 'Generating text'),
('common.scriptGenerated', 'common', 'Script generated successfully'),

-- Dashboard keys
('dashboard.openTool', 'dashboard', 'Open tool button'),
('dashboard.toolsSection', 'dashboard', 'Tools section'),
('dashboard.recentAdjustments', 'dashboard', 'Recent adjustments section'),
('auth.role', 'auth', 'Role label'),
('auth.status', 'auth', 'Status label'),
('auth.active', 'auth', 'Active status');

-- Insert English translations
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT ck.id, 'en', 
CASE ck.key_name
    -- Script Generator
    WHEN 'tools.scriptGenerator.title' THEN 'Google Ads Script Generator'
    WHEN 'tools.scriptGenerator.description' THEN 'Universal tool for creating custom Google Ads scripts with various automation functions.'
    WHEN 'tools.scriptGenerator.howItWorks.step1' THEN 'Fill in your campaign details, target URL, and budget.'
    WHEN 'tools.scriptGenerator.howItWorks.step2' THEN 'Craft compelling ads by adding multiple headlines and descriptions.'
    WHEN 'tools.scriptGenerator.howItWorks.step3' THEN 'List relevant keywords that will trigger your ads.'
    WHEN 'tools.scriptGenerator.howItWorks.step4' THEN 'Optionally, enable Telegram notifications for script execution updates.'
    WHEN 'tools.scriptGenerator.howItWorks.step5' THEN 'Click "Generate Script" to get a ready-to-use Google Ads script.'
    WHEN 'tools.scriptGenerator.sections.campaignDetails' THEN 'Campaign & Ad Group Details'
    WHEN 'tools.scriptGenerator.sections.adContent' THEN 'Ad Content'
    WHEN 'tools.scriptGenerator.sections.keywords' THEN 'Keywords'
    WHEN 'tools.scriptGenerator.sections.telegram' THEN 'Telegram Notifications (Optional)'
    WHEN 'tools.scriptGenerator.fields.finalUrl' THEN 'Final URL'
    WHEN 'tools.scriptGenerator.fields.campaignName' THEN 'Campaign Name'
    WHEN 'tools.scriptGenerator.fields.adGroupName' THEN 'Ad Group Name'
    WHEN 'tools.scriptGenerator.fields.dailyBudget' THEN 'Daily Budget'
    WHEN 'tools.scriptGenerator.fields.headlines' THEN 'Headlines (up to 15)'
    WHEN 'tools.scriptGenerator.fields.descriptions' THEN 'Descriptions (up to 4)'
    WHEN 'tools.scriptGenerator.fields.keyword' THEN 'Keyword'
    WHEN 'tools.scriptGenerator.fields.enableTelegram' THEN 'Enable Telegram Notifications'
    WHEN 'tools.scriptGenerator.fields.telegramBotToken' THEN 'Telegram Bot Token'
    WHEN 'tools.scriptGenerator.fields.telegramChatId' THEN 'Telegram Chat ID'
    WHEN 'tools.scriptGenerator.placeholders.headline' THEN 'Headline'
    WHEN 'tools.scriptGenerator.placeholders.description' THEN 'Description'
    WHEN 'tools.scriptGenerator.placeholders.keyword' THEN 'e.g., buy online widgets'
    WHEN 'tools.scriptGenerator.tooltips.finalUrl' THEN 'The landing page URL for your ads.'
    WHEN 'tools.scriptGenerator.tooltips.campaignName' THEN 'Name for your new campaign.'
    WHEN 'tools.scriptGenerator.tooltips.adGroupName' THEN 'Name for the primary ad group in this campaign.'
    WHEN 'tools.scriptGenerator.tooltips.dailyBudget' THEN 'Daily budget for the campaign (e.g., 50). Set to 0 for no budget (uses shared budget).'
    WHEN 'tools.scriptGenerator.tooltips.removeHeadline' THEN 'Remove Headline'
    WHEN 'tools.scriptGenerator.tooltips.removeDescription' THEN 'Remove Description'
    WHEN 'tools.scriptGenerator.tooltips.removeKeyword' THEN 'Remove Keyword'
    WHEN 'tools.scriptGenerator.tooltips.telegramBotToken' THEN 'Your Telegram Bot API Token.'
    WHEN 'tools.scriptGenerator.tooltips.telegramChatId' THEN 'Your Telegram User/Group Chat ID.'
    WHEN 'tools.scriptGenerator.buttons.addHeadline' THEN 'Add Headline'
    WHEN 'tools.scriptGenerator.buttons.addDescription' THEN 'Add Description'
    WHEN 'tools.scriptGenerator.buttons.addKeyword' THEN 'Add New Keyword'
    WHEN 'tools.scriptGenerator.buttons.generateScript' THEN 'Generate Script'
    WHEN 'tools.scriptGenerator.results.title' THEN 'Generated Google Ads Script'
    WHEN 'tools.scriptGenerator.errors.requiredFields' THEN 'Campaign Name, Ad Group Name, and Final URL are required.'
    WHEN 'tools.scriptGenerator.errors.invalidBudget' THEN 'Budget must be a positive number.'
    WHEN 'tools.scriptGenerator.errors.invalidUrl' THEN 'Please enter a valid Final URL (e.g., https://example.com).'
    WHEN 'tools.scriptGenerator.errors.telegramRequired' THEN 'Telegram Bot Token and Chat ID are required when Telegram notifications are enabled.'
    WHEN 'tools.scriptGenerator.errors.noAds' THEN 'Please add at least one ad with headlines and descriptions.'
    WHEN 'tools.scriptGenerator.errors.incompleteAds' THEN 'Please ensure all active ads have at least one headline and one description.'
    WHEN 'tools.scriptGenerator.errors.noKeywords' THEN 'Please add at least one keyword.'
    WHEN 'tools.scriptGenerator.success.generated' THEN 'Script generated successfully!'
    
    -- Budget Monitor
    WHEN 'tools.budgetMonitor.title' THEN 'Budget Monitor'
    WHEN 'tools.budgetMonitor.description' THEN 'Monitor campaign budgets and receive alerts when spending exceeds or falls below your defined thresholds.'
    WHEN 'tools.budgetMonitor.generated' THEN 'Generated Budget Monitor Script'
    WHEN 'tools.budgetMonitor.howItWorks' THEN 'How Budget Monitor Works'
    WHEN 'tools.budgetMonitor.howItWorks.step1' THEN 'Campaign Input: You provide a list of exact campaign names for monitoring (one per line).'
    WHEN 'tools.budgetMonitor.howItWorks.step2' THEN 'Pace Calculation: The script runs (ideally hourly) and calculates expected spend for the current time of day based on the campaign daily budget.'
    WHEN 'tools.budgetMonitor.howItWorks.step3' THEN 'Thresholds:'
    WHEN 'tools.budgetMonitor.howItWorks.step3a' THEN 'High Pace Threshold: If actual spend exceeds expected spend by this percentage (e.g., 120% means spending is 20% above pace), an alert is triggered.'
    WHEN 'tools.budgetMonitor.howItWorks.step3b' THEN 'Low Pace Threshold: If actual spend is below expected spend by this percentage (e.g., 80% means spending is 20% below pace), an alert is triggered.'
    WHEN 'tools.budgetMonitor.howItWorks.step4' THEN 'Timezone: The script automatically uses your Google Ads account timezone for accurate calculations.'
    WHEN 'tools.budgetMonitor.howItWorks.step5' THEN 'Alerts: If any campaign goes outside the defined thresholds, a summary of alerts is logged.'
    WHEN 'tools.budgetMonitor.howItWorks.step6' THEN 'Telegram Notifications: If configured, these alerts (or messages that all is well) are sent via Telegram.'
    WHEN 'tools.budgetMonitor.howItWorks.important' THEN 'Important: This script should be scheduled to run frequently (e.g., hourly) in your Google Ads account for timely monitoring. Make sure campaign names match exactly.'
    WHEN 'tools.budgetMonitor.sections.campaigns' THEN 'Campaign Names'
    WHEN 'tools.budgetMonitor.sections.thresholds' THEN 'Pacing Thresholds (%)'
    WHEN 'tools.budgetMonitor.sections.notifications' THEN 'Telegram Notifications (Optional)'
    WHEN 'tools.budgetMonitor.fields.campaignNames' THEN 'Campaign Names (one per line)'
    WHEN 'tools.budgetMonitor.fields.highThreshold' THEN 'High Pacing Threshold (%)'
    WHEN 'tools.budgetMonitor.fields.lowThreshold' THEN 'Low Pacing Threshold (%)'
    WHEN 'tools.budgetMonitor.fields.telegramToken' THEN 'Telegram Bot Token'
    WHEN 'tools.budgetMonitor.fields.telegramChatId' THEN 'Telegram Chat ID'
    WHEN 'tools.budgetMonitor.errors.noCampaigns' THEN 'Please enter at least one campaign name to monitor.'
    WHEN 'tools.budgetMonitor.errors.telegramRequired' THEN 'Please enter both Telegram Bot Token and Chat ID for notifications.'
    WHEN 'tools.budgetMonitor.errors.invalidThresholds' THEN 'High and Low Pacing Thresholds must be valid numbers.'
    WHEN 'tools.budgetMonitor.errors.thresholdOrder' THEN 'Low Pacing Threshold must be less than High Pacing Threshold.'
    
    -- Performance Max
    WHEN 'tools.performanceMax.title' THEN 'Performance Max Asset Analyzer'
    WHEN 'tools.performanceMax.description' THEN 'Analyze and identify low-performing assets in your Performance Max campaigns for optimization.'
    WHEN 'tools.performanceMax.sections.campaign' THEN 'Campaign Settings'
    WHEN 'tools.performanceMax.sections.notifications' THEN 'Notification Settings'
    WHEN 'tools.performanceMax.sections.telegramDetails' THEN 'Telegram Details'
    WHEN 'tools.performanceMax.fields.campaignPattern' THEN 'Campaign Name Pattern'
    WHEN 'tools.performanceMax.fields.dateRange' THEN 'Date Range'
    WHEN 'tools.performanceMax.fields.emailAddress' THEN 'Email Address'
    WHEN 'tools.performanceMax.fields.telegramSummary' THEN 'Receive summary via Telegram'
    WHEN 'tools.performanceMax.fields.telegramToken' THEN 'Telegram Bot Token'
    WHEN 'tools.performanceMax.fields.telegramChatId' THEN 'Telegram Chat ID'
    WHEN 'tools.performanceMax.placeholders.campaignPattern' THEN 'e.g., My_PMax_Campaign'
    WHEN 'tools.performanceMax.placeholders.telegramToken' THEN 'Enter bot token'
    WHEN 'tools.performanceMax.placeholders.telegramChatId' THEN 'Enter Chat ID'
    WHEN 'tools.performanceMax.errors.campaignRequired' THEN 'Campaign Name Pattern is required for Performance Max campaigns.'
    WHEN 'tools.performanceMax.errors.invalidEmail' THEN 'Please enter a valid email address for notifications.'
    WHEN 'tools.performanceMax.errors.telegramRequired' THEN 'Please enter both Telegram Bot Token and Chat ID for Telegram notifications.'
    
    -- Device Bid
    WHEN 'tools.deviceBid.title' THEN 'Device Bid Adjuster'
    WHEN 'tools.deviceBid.description' THEN 'Optimize your campaign performance by automatically adjusting bids for different devices based on CPC thresholds.'
    
    -- Common
    WHEN 'common.backToForm' THEN 'Back to Form'
    WHEN 'common.generateScript' THEN 'Generate Script'
    WHEN 'common.enableTelegram' THEN 'Enable Telegram Notifications'
    WHEN 'common.generating' THEN 'Generating...'
    WHEN 'common.scriptGenerated' THEN 'Script generated successfully!'
    
    -- Dashboard
    WHEN 'dashboard.openTool' THEN 'Open Tool'
    WHEN 'dashboard.toolsSection' THEN 'Tools'
    WHEN 'dashboard.recentAdjustments' THEN 'Recent Budget Adjustments'
    WHEN 'auth.role' THEN 'Role'
    WHEN 'auth.status' THEN 'Status'
    WHEN 'auth.active' THEN 'Active'
END
FROM content_keys ck 
WHERE ck.key_name IN (
    'tools.scriptGenerator.title', 'tools.scriptGenerator.description', 'tools.scriptGenerator.howItWorks.step1',
    'tools.scriptGenerator.howItWorks.step2', 'tools.scriptGenerator.howItWorks.step3', 'tools.scriptGenerator.howItWorks.step4',
    'tools.scriptGenerator.howItWorks.step5', 'tools.scriptGenerator.sections.campaignDetails', 'tools.scriptGenerator.sections.adContent',
    'tools.scriptGenerator.sections.keywords', 'tools.scriptGenerator.sections.telegram', 'tools.scriptGenerator.fields.finalUrl',
    'tools.scriptGenerator.fields.campaignName', 'tools.scriptGenerator.fields.adGroupName', 'tools.scriptGenerator.fields.dailyBudget',
    'tools.scriptGenerator.fields.headlines', 'tools.scriptGenerator.fields.descriptions', 'tools.scriptGenerator.fields.keyword',
    'tools.scriptGenerator.fields.enableTelegram', 'tools.scriptGenerator.fields.telegramBotToken', 'tools.scriptGenerator.fields.telegramChatId',
    'tools.scriptGenerator.placeholders.headline', 'tools.scriptGenerator.placeholders.description', 'tools.scriptGenerator.placeholders.keyword',
    'tools.scriptGenerator.tooltips.finalUrl', 'tools.scriptGenerator.tooltips.campaignName', 'tools.scriptGenerator.tooltips.adGroupName',
    'tools.scriptGenerator.tooltips.dailyBudget', 'tools.scriptGenerator.tooltips.removeHeadline', 'tools.scriptGenerator.tooltips.removeDescription',
    'tools.scriptGenerator.tooltips.removeKeyword', 'tools.scriptGenerator.tooltips.telegramBotToken', 'tools.scriptGenerator.tooltips.telegramChatId',
    'tools.scriptGenerator.buttons.addHeadline', 'tools.scriptGenerator.buttons.addDescription', 'tools.scriptGenerator.buttons.addKeyword',
    'tools.scriptGenerator.buttons.generateScript', 'tools.scriptGenerator.results.title', 'tools.scriptGenerator.errors.requiredFields',
    'tools.scriptGenerator.errors.invalidBudget', 'tools.scriptGenerator.errors.invalidUrl', 'tools.scriptGenerator.errors.telegramRequired',
    'tools.scriptGenerator.errors.noAds', 'tools.scriptGenerator.errors.incompleteAds', 'tools.scriptGenerator.errors.noKeywords',
    'tools.scriptGenerator.success.generated', 'tools.budgetMonitor.title', 'tools.budgetMonitor.description', 'tools.budgetMonitor.generated',
    'tools.budgetMonitor.howItWorks', 'tools.budgetMonitor.howItWorks.step1', 'tools.budgetMonitor.howItWorks.step2', 'tools.budgetMonitor.howItWorks.step3',
    'tools.budgetMonitor.howItWorks.step3a', 'tools.budgetMonitor.howItWorks.step3b', 'tools.budgetMonitor.howItWorks.step4', 'tools.budgetMonitor.howItWorks.step5',
    'tools.budgetMonitor.howItWorks.step6', 'tools.budgetMonitor.howItWorks.important', 'tools.budgetMonitor.sections.campaigns', 'tools.budgetMonitor.sections.thresholds',
    'tools.budgetMonitor.sections.notifications', 'tools.budgetMonitor.fields.campaignNames', 'tools.budgetMonitor.fields.highThreshold', 'tools.budgetMonitor.fields.lowThreshold',
    'tools.budgetMonitor.fields.telegramToken', 'tools.budgetMonitor.fields.telegramChatId', 'tools.budgetMonitor.errors.noCampaigns', 'tools.budgetMonitor.errors.telegramRequired',
    'tools.budgetMonitor.errors.invalidThresholds', 'tools.budgetMonitor.errors.thresholdOrder', 'tools.performanceMax.title', 'tools.performanceMax.description',
    'tools.performanceMax.sections.campaign', 'tools.performanceMax.sections.notifications', 'tools.performanceMax.sections.telegramDetails', 'tools.performanceMax.fields.campaignPattern',
    'tools.performanceMax.fields.dateRange', 'tools.performanceMax.fields.emailAddress', 'tools.performanceMax.fields.telegramSummary', 'tools.performanceMax.fields.telegramToken',
    'tools.performanceMax.fields.telegramChatId', 'tools.performanceMax.placeholders.campaignPattern', 'tools.performanceMax.placeholders.telegramToken', 'tools.performanceMax.placeholders.telegramChatId',
    'tools.performanceMax.errors.campaignRequired', 'tools.performanceMax.errors.invalidEmail', 'tools.performanceMax.errors.telegramRequired', 'tools.deviceBid.title',
    'tools.deviceBid.description', 'common.backToForm', 'common.generateScript', 'common.enableTelegram', 'common.generating', 'common.scriptGenerated',
    'dashboard.openTool', 'dashboard.toolsSection', 'dashboard.recentAdjustments', 'auth.role', 'auth.status', 'auth.active'
);

-- Insert Ukrainian translations  
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT ck.id, 'ua',
CASE ck.key_name
    -- Script Generator
    WHEN 'tools.scriptGenerator.title' THEN 'Генератор Google Ads скриптів'
    WHEN 'tools.scriptGenerator.description' THEN 'Універсальний інструмент для створення користувацьких Google Ads скриптів з різними функціями автоматизації.'
    WHEN 'tools.scriptGenerator.howItWorks.step1' THEN 'Заповніть деталі кампанії, цільову URL-адресу та бюджет.'
    WHEN 'tools.scriptGenerator.howItWorks.step2' THEN 'Створіть привабливі оголошення, додавши кілька заголовків і описів.'
    WHEN 'tools.scriptGenerator.howItWorks.step3' THEN 'Перерахуйте відповідні ключові слова, які запускатимуть ваші оголошення.'
    WHEN 'tools.scriptGenerator.howItWorks.step4' THEN 'За бажанням увімкніть сповіщення Telegram для оновлень виконання скрипта.'
    WHEN 'tools.scriptGenerator.howItWorks.step5' THEN 'Натисніть "Згенерувати скрипт", щоб отримати готовий до використання Google Ads скрипт.'
    WHEN 'tools.scriptGenerator.sections.campaignDetails' THEN 'Деталі кампанії та групи оголошень'
    WHEN 'tools.scriptGenerator.sections.adContent' THEN 'Зміст оголошення'
    WHEN 'tools.scriptGenerator.sections.keywords' THEN 'Ключові слова'
    WHEN 'tools.scriptGenerator.sections.telegram' THEN 'Сповіщення Telegram (необов\'язково)'
    WHEN 'tools.scriptGenerator.fields.finalUrl' THEN 'Фінальна URL'
    WHEN 'tools.scriptGenerator.fields.campaignName' THEN 'Назва кампанії'
    WHEN 'tools.scriptGenerator.fields.adGroupName' THEN 'Назва групи оголошень'
    WHEN 'tools.scriptGenerator.fields.dailyBudget' THEN 'Щоденний бюджет'
    WHEN 'tools.scriptGenerator.fields.headlines' THEN 'Заголовки (до 15)'
    WHEN 'tools.scriptGenerator.fields.descriptions' THEN 'Описи (до 4)'
    WHEN 'tools.scriptGenerator.fields.keyword' THEN 'Ключове слово'
    WHEN 'tools.scriptGenerator.fields.enableTelegram' THEN 'Увімкнути сповіщення Telegram'
    WHEN 'tools.scriptGenerator.fields.telegramBotToken' THEN 'Токен Telegram бота'
    WHEN 'tools.scriptGenerator.fields.telegramChatId' THEN 'ID чату Telegram'
    WHEN 'tools.scriptGenerator.placeholders.headline' THEN 'Заголовок'
    WHEN 'tools.scriptGenerator.placeholders.description' THEN 'Опис'
    WHEN 'tools.scriptGenerator.placeholders.keyword' THEN 'наприклад, купити онлайн віджети'
    WHEN 'tools.scriptGenerator.tooltips.finalUrl' THEN 'URL-адреса цільової сторінки для ваших оголошень.'
    WHEN 'tools.scriptGenerator.tooltips.campaignName' THEN 'Назва для вашої нової кампанії.'
    WHEN 'tools.scriptGenerator.tooltips.adGroupName' THEN 'Назва для основної групи оголошень у цій кампанії.'
    WHEN 'tools.scriptGenerator.tooltips.dailyBudget' THEN 'Щоденний бюджет для кампанії (наприклад, 50). Встановіть 0 для відсутності бюджету (використовує спільний бюджет).'
    WHEN 'tools.scriptGenerator.tooltips.removeHeadline' THEN 'Видалити заголовок'
    WHEN 'tools.scriptGenerator.tooltips.removeDescription' THEN 'Видалити опис'
    WHEN 'tools.scriptGenerator.tooltips.removeKeyword' THEN 'Видалити ключове слово'
    WHEN 'tools.scriptGenerator.tooltips.telegramBotToken' THEN 'Ваш API токен Telegram бота.'
    WHEN 'tools.scriptGenerator.tooltips.telegramChatId' THEN 'Ваш ID користувача/групи чату Telegram.'
    WHEN 'tools.scriptGenerator.buttons.addHeadline' THEN 'Додати заголовок'
    WHEN 'tools.scriptGenerator.buttons.addDescription' THEN 'Додати опис'
    WHEN 'tools.scriptGenerator.buttons.addKeyword' THEN 'Додати нове ключове слово'
    WHEN 'tools.scriptGenerator.buttons.generateScript' THEN 'Згенерувати скрипт'
    WHEN 'tools.scriptGenerator.results.title' THEN 'Згенерований Google Ads скрипт'
    WHEN 'tools.scriptGenerator.errors.requiredFields' THEN 'Назва кампанії, назва групи оголошень та фінальна URL є обов\'язковими.'
    WHEN 'tools.scriptGenerator.errors.invalidBudget' THEN 'Бюджет має бути позитивним числом.'
    WHEN 'tools.scriptGenerator.errors.invalidUrl' THEN 'Будь ласка, введіть дійсну фінальну URL (наприклад, https://example.com).'
    WHEN 'tools.scriptGenerator.errors.telegramRequired' THEN 'Токен Telegram бота та ID чату є обов\'язковими, коли увімкнені сповіщення Telegram.'
    WHEN 'tools.scriptGenerator.errors.noAds' THEN 'Будь ласка, додайте принаймні одне оголошення із заголовками та описами.'
    WHEN 'tools.scriptGenerator.errors.incompleteAds' THEN 'Будь ласка, переконайтеся, що всі активні оголошення мають принаймні один заголовок та один опис.'
    WHEN 'tools.scriptGenerator.errors.noKeywords' THEN 'Будь ласка, додайте принаймні одне ключове слово.'
    WHEN 'tools.scriptGenerator.success.generated' THEN 'Скрипт успішно згенерований!'
    
    -- Budget Monitor
    WHEN 'tools.budgetMonitor.title' THEN 'Монітор бюджету'
    WHEN 'tools.budgetMonitor.description' THEN 'Відстежуйте бюджети кампаній та отримуйте сповіщення, коли витрати перевищують або падають нижче визначених порогів.'
    WHEN 'tools.budgetMonitor.generated' THEN 'Згенерований скрипт моніторингу бюджету'
    WHEN 'tools.budgetMonitor.howItWorks' THEN 'Як працює монітор бюджету'
    WHEN 'tools.budgetMonitor.howItWorks.step1' THEN 'Введення кампаній: Ви надаєте список точних назв кампаній для моніторингу (по одній на рядок).'
    WHEN 'tools.budgetMonitor.howItWorks.step2' THEN 'Розрахунок темпу: Скрипт запускається (в ідеалі щогодини) і розраховує очікувані витрати на поточний час дня на основі щоденного бюджету кампанії.'
    WHEN 'tools.budgetMonitor.howItWorks.step3' THEN 'Пороги:'
    WHEN 'tools.budgetMonitor.howItWorks.step3a' THEN 'Високий поріг темпу: Якщо фактичні витрати перевищують очікувані витрати на цей відсоток (наприклад, 120% означає, що витрати на 20% перевищують темп), спрацьовує сповіщення.'
    WHEN 'tools.budgetMonitor.howItWorks.step3b' THEN 'Низький поріг темпу: Якщо фактичні витрати нижче очікуваних витрат на цей відсоток (наприклад, 80% означає, що витрати на 20% нижче темпу), спрацьовує сповіщення.'
    WHEN 'tools.budgetMonitor.howItWorks.step4' THEN 'Часовий пояс: Скрипт автоматично використовує часовий пояс вашого облікового запису Google Ads для точних розрахунків.'
    WHEN 'tools.budgetMonitor.howItWorks.step5' THEN 'Сповіщення: Якщо будь-яка кампанія виходить за межі визначених порогів, реєструється зведення сповіщень.'
    WHEN 'tools.budgetMonitor.howItWorks.step6' THEN 'Сповіщення Telegram: Якщо налаштовано, ці сповіщення (або повідомлення про те, що все добре) надсилаються через Telegram.'
    WHEN 'tools.budgetMonitor.howItWorks.important' THEN 'Важливо: Цей скрипт слід запланувати для частого запуску (наприклад, щогодини) у вашому обліковому записі Google Ads для своєчасного моніторингу. Переконайтеся, що назви кампаній точно збігаються.'
    WHEN 'tools.budgetMonitor.sections.campaigns' THEN 'Назви кампаній'
    WHEN 'tools.budgetMonitor.sections.thresholds' THEN 'Пороги темпу (%)'
    WHEN 'tools.budgetMonitor.sections.notifications' THEN 'Сповіщення Telegram (необов\'язково)'
    WHEN 'tools.budgetMonitor.fields.campaignNames' THEN 'Назви кампаній (по одній на рядок)'
    WHEN 'tools.budgetMonitor.fields.highThreshold' THEN 'Високий поріг темпу (%)'
    WHEN 'tools.budgetMonitor.fields.lowThreshold' THEN 'Низький поріг темпу (%)'
    WHEN 'tools.budgetMonitor.fields.telegramToken' THEN 'Токен Telegram бота'
    WHEN 'tools.budgetMonitor.fields.telegramChatId' THEN 'ID чату Telegram'
    WHEN 'tools.budgetMonitor.errors.noCampaigns' THEN 'Будь ласка, введіть принаймні одну назву кампанії для моніторингу.'
    WHEN 'tools.budgetMonitor.errors.telegramRequired' THEN 'Будь ласка, введіть токен Telegram бота та ID чату для сповіщень.'
    WHEN 'tools.budgetMonitor.errors.invalidThresholds' THEN 'Високий та низький пороги темпу мають бути дійсними числами.'
    WHEN 'tools.budgetMonitor.errors.thresholdOrder' THEN 'Низький поріг темпу має бути менше високого порогу темпу.'
    
    -- Performance Max
    WHEN 'tools.performanceMax.title' THEN 'Аналізатор ресурсів Performance Max'
    WHEN 'tools.performanceMax.description' THEN 'Аналізуйте та визначайте низькопродуктивні ресурси у ваших кампаніях Performance Max для оптимізації.'
    WHEN 'tools.performanceMax.sections.campaign' THEN 'Налаштування кампанії'
    WHEN 'tools.performanceMax.sections.notifications' THEN 'Налаштування сповіщень'
    WHEN 'tools.performanceMax.sections.telegramDetails' THEN 'Деталі Telegram'
    WHEN 'tools.performanceMax.fields.campaignPattern' THEN 'Шаблон назви кампанії'
    WHEN 'tools.performanceMax.fields.dateRange' THEN 'Діапазон дат'
    WHEN 'tools.performanceMax.fields.emailAddress' THEN 'Електронна адреса'
    WHEN 'tools.performanceMax.fields.telegramSummary' THEN 'Отримувати зведення через Telegram'
    WHEN 'tools.performanceMax.fields.telegramToken' THEN 'Токен Telegram бота'
    WHEN 'tools.performanceMax.fields.telegramChatId' THEN 'ID чату Telegram'
    WHEN 'tools.performanceMax.placeholders.campaignPattern' THEN 'наприклад, My_PMax_Campaign'
    WHEN 'tools.performanceMax.placeholders.telegramToken' THEN 'Введіть токен бота'
    WHEN 'tools.performanceMax.placeholders.telegramChatId' THEN 'Введіть ID чату'
    WHEN 'tools.performanceMax.errors.campaignRequired' THEN 'Шаблон назви кампанії є обов\'язковим для кампаній Performance Max.'
    WHEN 'tools.performanceMax.errors.invalidEmail' THEN 'Будь ласка, введіть дійсну електронну адресу для сповіщень.'
    WHEN 'tools.performanceMax.errors.telegramRequired' THEN 'Будь ласка, введіть токен Telegram бота та ID чату для сповіщень Telegram.'
    
    -- Device Bid
    WHEN 'tools.deviceBid.title' THEN 'Коригувач ставок за пристроями'
    WHEN 'tools.deviceBid.description' THEN 'Оптимізуйте продуктивність кампанії, автоматично коригуючи ставки для різних пристроїв на основі порогів CPC.'
    
    -- Common
    WHEN 'common.backToForm' THEN 'Повернутися до форми'
    WHEN 'common.generateScript' THEN 'Згенерувати скрипт'
    WHEN 'common.enableTelegram' THEN 'Увімкнути сповіщення Telegram'
    WHEN 'common.generating' THEN 'Генерація...'
    WHEN 'common.scriptGenerated' THEN 'Скрипт успішно згенерований!'
    
    -- Dashboard
    WHEN 'dashboard.openTool' THEN 'Відкрити інструмент'
    WHEN 'dashboard.toolsSection' THEN 'Інструменти'
    WHEN 'dashboard.recentAdjustments' THEN 'Останні коригування бюджету'
    WHEN 'auth.role' THEN 'Роль'
    WHEN 'auth.status' THEN 'Статус'
    WHEN 'auth.active' THEN 'Активний'
END
FROM content_keys ck 
WHERE ck.key_name IN (
    'tools.scriptGenerator.title', 'tools.scriptGenerator.description', 'tools.scriptGenerator.howItWorks.step1',
    'tools.scriptGenerator.howItWorks.step2', 'tools.scriptGenerator.howItWorks.step3', 'tools.scriptGenerator.howItWorks.step4',
    'tools.scriptGenerator.howItWorks.step5', 'tools.scriptGenerator.sections.campaignDetails', 'tools.scriptGenerator.sections.adContent',
    'tools.scriptGenerator.sections.keywords', 'tools.scriptGenerator.sections.telegram', 'tools.scriptGenerator.fields.finalUrl',
    'tools.scriptGenerator.fields.campaignName', 'tools.scriptGenerator.fields.adGroupName', 'tools.scriptGenerator.fields.dailyBudget',
    'tools.scriptGenerator.fields.headlines', 'tools.scriptGenerator.fields.descriptions', 'tools.scriptGenerator.fields.keyword',
    'tools.scriptGenerator.fields.enableTelegram', 'tools.scriptGenerator.fields.telegramBotToken', 'tools.scriptGenerator.fields.telegramChatId',
    'tools.scriptGenerator.placeholders.headline', 'tools.scriptGenerator.placeholders.description', 'tools.scriptGenerator.placeholders.keyword',
    'tools.scriptGenerator.tooltips.finalUrl', 'tools.scriptGenerator.tooltips.campaignName', 'tools.scriptGenerator.tooltips.adGroupName',
    'tools.scriptGenerator.tooltips.dailyBudget', 'tools.scriptGenerator.tooltips.removeHeadline', 'tools.scriptGenerator.tooltips.removeDescription',
    'tools.scriptGenerator.tooltips.removeKeyword', 'tools.scriptGenerator.tooltips.telegramBotToken', 'tools.scriptGenerator.tooltips.telegramChatId',
    'tools.scriptGenerator.buttons.addHeadline', 'tools.scriptGenerator.buttons.addDescription', 'tools.scriptGenerator.buttons.addKeyword',
    'tools.scriptGenerator.buttons.generateScript', 'tools.scriptGenerator.results.title', 'tools.scriptGenerator.errors.requiredFields',
    'tools.scriptGenerator.errors.invalidBudget', 'tools.scriptGenerator.errors.invalidUrl', 'tools.scriptGenerator.errors.telegramRequired',
    'tools.scriptGenerator.errors.noAds', 'tools.scriptGenerator.errors.incompleteAds', 'tools.scriptGenerator.errors.noKeywords',
    'tools.scriptGenerator.success.generated', 'tools.budgetMonitor.title', 'tools.budgetMonitor.description', 'tools.budgetMonitor.generated',
    'tools.budgetMonitor.howItWorks', 'tools.budgetMonitor.howItWorks.step1', 'tools.budgetMonitor.howItWorks.step2', 'tools.budgetMonitor.howItWorks.step3',
    'tools.budgetMonitor.howItWorks.step3a', 'tools.budgetMonitor.howItWorks.step3b', 'tools.budgetMonitor.howItWorks.step4', 'tools.budgetMonitor.howItWorks.step5',
    'tools.budgetMonitor.howItWorks.step6', 'tools.budgetMonitor.howItWorks.important', 'tools.budgetMonitor.sections.campaigns', 'tools.budgetMonitor.sections.thresholds',
    'tools.budgetMonitor.sections.notifications', 'tools.budgetMonitor.fields.campaignNames', 'tools.budgetMonitor.fields.highThreshold', 'tools.budgetMonitor.fields.lowThreshold',
    'tools.budgetMonitor.fields.telegramToken', 'tools.budgetMonitor.fields.telegramChatId', 'tools.budgetMonitor.errors.noCampaigns', 'tools.budgetMonitor.errors.telegramRequired',
    'tools.budgetMonitor.errors.invalidThresholds', 'tools.budgetMonitor.errors.thresholdOrder', 'tools.performanceMax.title', 'tools.performanceMax.description',
    'tools.performanceMax.sections.campaign', 'tools.performanceMax.sections.notifications', 'tools.performanceMax.sections.telegramDetails', 'tools.performanceMax.fields.campaignPattern',
    'tools.performanceMax.fields.dateRange', 'tools.performanceMax.fields.emailAddress', 'tools.performanceMax.fields.telegramSummary', 'tools.performanceMax.fields.telegramToken',
    'tools.performanceMax.fields.telegramChatId', 'tools.performanceMax.placeholders.campaignPattern', 'tools.performanceMax.placeholders.telegramToken', 'tools.performanceMax.placeholders.telegramChatId',
    'tools.performanceMax.errors.campaignRequired', 'tools.performanceMax.errors.invalidEmail', 'tools.performanceMax.errors.telegramRequired', 'tools.deviceBid.title',
    'tools.deviceBid.description', 'common.backToForm', 'common.generateScript', 'common.enableTelegram', 'common.generating', 'common.scriptGenerated',
    'dashboard.openTool', 'dashboard.toolsSection', 'dashboard.recentAdjustments', 'auth.role', 'auth.status', 'auth.active'
);