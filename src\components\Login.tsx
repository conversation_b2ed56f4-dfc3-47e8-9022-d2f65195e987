import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from './ui/Button';
import { Eye, EyeOff } from 'lucide-react';
import { useApiAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { login, error, isAuthenticated, isLoading } = useApiAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  
  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);
    
    console.log('🔐 Login attempt:', { email, password: password.replace(/./g, '*') });
    
    try {
      const success = await login(email, password);
      console.log('🔐 Login result:', success);
      if (success) {
        console.log('✅ Login successful, redirecting to dashboard');
        navigate('/dashboard');
      } else {
        console.log('❌ Login failed:', error);
        setErrorMessage(error || 'Invalid email or password');
      }
    } catch (err) {
      console.error('💥 Login error:', err);
      setErrorMessage('An error occurred during login. Please try again.');
    }
  };

  const clearCache = () => {
    console.log('🧹 Clearing all cache...');
    localStorage.clear();
    sessionStorage.clear();
    // Clear all cookies
    document.cookie.split(";").forEach(function(c) {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });
    window.location.reload();
  };

  return (
    <section className="w-full flex flex-col justify-center items-center">
      <div className="w-full max-w-md">
        <div className="bg-gradient-to-br from-gray-800/90 to-gray-900/90 rounded-xl shadow-2xl p-8 backdrop-blur-sm border border-white/10">
          <h2 className="text-3xl font-bold mb-8 text-center text-white">{t('auth.login')}</h2>
          

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">{t('auth.email')}</label>
              <div className="relative">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg bg-gray-700/50 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                  required
                />

              </div>
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">{t('auth.password')}</label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg bg-gray-700/50 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="••••••••••••"
                  required
                />
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-md bg-gray-600/50 text-white hover:bg-gray-500/50 transition-colors"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>
            
            {errorMessage && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                <span className="block sm:inline">{errorMessage}</span>
              </div>
            )}
            
            <Button
              type="submit"
              variant="secondary"
              className="w-full py-3 text-lg font-semibold mt-8 bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-200"
              disabled={isLoading}
            >
              {isLoading ? t('common.loading') : t('auth.loginButton')}
            </Button>
          </form>

          <div className="mt-4 text-center">
            <button
              type="button"
              onClick={clearCache}
              className="text-sm text-gray-400 hover:text-white underline"
            >
              🧹 Clear Cache & Reload
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Login;
