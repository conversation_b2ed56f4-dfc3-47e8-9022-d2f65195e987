import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../contexts/LanguageContext';

type SEOProps = {
  title?: string;
  description?: string;
  path: string;
  type?: 'website' | 'article';
  image?: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tag?: string;
  useDbSeo?: boolean; // Use database SEO data
};

const SEOComponent: React.FC<SEOProps> = ({
  title,
  description,
  path,
  type = 'website',
  image = '/og-image.jpg',
  publishedTime,
  modifiedTime,
  author = 'gAds Team',
  section,
  tag,
  useDbSeo = true,
}) => {
  const { t, language } = useLanguage();
  const siteUrl = 'https://gads-supercharge.online';
  
  // Use database SEO data if available and useDbSeo is true
  const finalTitle = useDbSeo && t(`seo.meta_title_${language}`) !== `seo.meta_title_${language}` 
    ? t(`seo.meta_title_${language}`) 
    : title || 'gAds Supercharge - Google Ads Automation Platform';
    
  const finalDescription = useDbSeo && t(`seo.meta_description_${language}`) !== `seo.meta_description_${language}`
    ? t(`seo.meta_description_${language}`)
    : description || 'Professional Google Ads automation platform with script generators and performance analysis tools.';
  const fullUrl = `${siteUrl}${path}`;
  const fullImageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;

  return (
    <Helmet>
      {/* Standard metadata */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={useDbSeo ? t(`seo.keywords_${language}`) : 'google ads, automation, script generator, ppc tools'} />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:site_name" content="gAds Supercharge" />
      <meta property="og:locale" content={language === 'ua' ? 'uk_UA' : 'en_US'} />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:url" content={fullUrl} />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:creator" content="@gads_supercharge" />

      {/* Additional meta tags */}
      <meta name="robots" content="index, follow" />
      <meta name="theme-color" content="#1e40af" />

      {/* Schema.org markup for Google */}
      <script type="application/ld+json">
        {JSON.stringify({
          '@context': 'https://schema.org',
          '@type': type === 'article' ? 'Article' : 'WebPage',
          name: finalTitle,
          description: finalDescription,
          url: fullUrl,
          publisher: {
            '@type': 'Organization',
            name: 'gAds',
            logo: {
              '@type': 'ImageObject',
              url: `${siteUrl}/logo-192x192.png`,
            },
          },
          image: fullImageUrl,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': fullUrl,
          },
          ...(type === 'article' && {
            author: {
              '@type': 'Person',
              name: author,
            },
            datePublished: publishedTime,
            dateModified: modifiedTime || publishedTime,
            articleSection: section,
            articleTag: tag,
            publisher: {
              '@type': 'Organization',
              name: 'gAds',
              logo: {
                '@type': 'ImageObject',
                url: `${siteUrl}/logo-192x192.png`,
              },
            },
          }),
        })}
      </script>
    </Helmet>
  );
};

export default SEOComponent;
