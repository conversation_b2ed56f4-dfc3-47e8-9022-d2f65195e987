import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2 } from 'lucide-react';

const BlogPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // In a real app, you would fetch this from your API or config
    const blogUrl = '/blog.txt';
    
    const fetchBlogUrl = async () => {
      try {
        const response = await fetch(blogUrl);
        if (response.ok) {
          const url = (await response.text()).trim();
          // Open blog in new tab
          window.open(url, '_blank', 'noopener,noreferrer');
          // Redirect to home after opening in new tab
          navigate('/');
        } else {
          throw new Error('Failed to fetch blog URL');
        }
      } catch (error) {
        console.error('Error redirecting to blog:', error);
        // Fallback to home if there's an error
        navigate('/');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogUrl();
  }, [navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <Loader2 className="h-12 w-12 text-blue-400 animate-spin mx-auto mb-4" />
          <p className="text-blue-100 text-lg">Redirecting to our blog...</p>
        </div>
      </div>
    );
  }

  return null;
};

export default BlogPage;
