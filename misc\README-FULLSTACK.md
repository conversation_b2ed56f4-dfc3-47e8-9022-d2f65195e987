# gAds Supercharge - Full-Stack Application

A comprehensive Google Ads automation and management platform with React frontend, Node.js backend, and PostgreSQL database.

## 🏗️ Architecture

- **Frontend**: React + TypeScript + Vite
- **Backend**: Node.js + Express.js + PostgreSQL
- **Database**: PostgreSQL with multilingual content management
- **Deployment**: Docker + Docker Compose
- **Authentication**: JWT-based with session tracking
- **Internationalization**: Database-driven content (EN/UA)

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Git

### 1. Clone and Setup

```bash
git clone <repository-url>
cd gads-supercharge

# Copy environment files
cp backend/.env.example backend/.env

# Edit backend/.env with your configuration
```

### 2. Start with Docker Compose

```bash
# Start all services (PostgreSQL, Backend, Frontend)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 3. Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Database**: localhost:5432

### 4. Default Login Credentials

```
Admin User:
Email: <EMAIL>
Password: admin123

Regular User:
Email: <EMAIL>
Password: user123
```

## 🛠️ Development Setup

### Backend Development

```bash
cd backend

# Install dependencies
npm install

# Set up environment
cp .env.example .env

# Start PostgreSQL (if not using Docker)
# Update .env with your database credentials

# Run migrations and seed data
npm run migrate
npm run seed

# Start development server
npm run dev
```

### Frontend Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## 📊 Database Schema

### Core Tables

- **users**: User authentication and preferences
- **content_keys**: Multilingual content keys
- **content_translations**: Language-specific translations
- **user_sessions**: Session tracking with browser info
- **user_activities**: Comprehensive activity logging
- **user_preferences**: User-specific settings

### Features

- ✅ Multilingual content management (EN/UA)
- ✅ User activity tracking and analytics
- ✅ Session management with device info
- ✅ JWT-based authentication
- ✅ Role-based access control
- ✅ Real-time language switching

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/verify` - Token verification

### Content Management
- `GET /api/content/:language` - Get all content for language
- `GET /api/content/:language/category/:category` - Get category content
- `POST /api/content` - Create/update content (Admin only)

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/language` - Update language preference
- `PUT /api/user/preferences` - Update user preferences
- `GET /api/user/sessions` - Get user sessions

### Activity Tracking
- `GET /api/activity/my` - Get user's activities
- `GET /api/activity/stats` - Get activity statistics
- `GET /api/activity/all` - Get all activities (Admin only)

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
NODE_ENV=production
PORT=3001
DB_HOST=postgres
DB_PORT=5432
DB_NAME=gads_db
DB_USER=gads_user
DB_PASSWORD=gads_password
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=http://localhost:5173
```

#### Frontend (Vite)
```env
VITE_API_URL=http://localhost:3001/api
VITE_NODE_ENV=production
```

## 🐳 Docker Configuration

### Services

1. **PostgreSQL**: Database with automatic initialization
2. **Backend**: Node.js API server with health checks
3. **Frontend**: Nginx-served React application
4. **Redis**: Session storage (optional)

### Health Checks

All services include health checks for monitoring:
- Database: `pg_isready`
- Backend: `/health` endpoint
- Frontend: Nginx status

## 📈 Features

### Language Management
- Database-driven translations
- Real-time language switching
- User preference persistence
- Fallback to static translations

### User Activity Tracking
- Login/logout events
- Page views and API calls
- Tool usage analytics
- Session management
- Browser and device info

### Security
- JWT authentication
- Session validation
- Rate limiting
- CORS protection
- SQL injection prevention
- XSS protection

## 🔍 Monitoring

### Logs
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres
```

### Database Access
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U gads_user -d gads_db

# View tables
\dt

# View content
SELECT * FROM content_translations WHERE language_code = 'en' LIMIT 10;
```

## 🚀 Production Deployment

### 1. Environment Setup
```bash
# Update environment variables for production
cp backend/.env.example backend/.env.production

# Set secure JWT secret
# Configure production database
# Set CORS origins
```

### 2. Build and Deploy
```bash
# Build production images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 3. SSL/TLS Setup
- Configure reverse proxy (Nginx/Traefik)
- Set up SSL certificates
- Update CORS origins

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test
```

### Frontend Tests
```bash
npm test
```

### API Testing
```bash
# Test health endpoint
curl http://localhost:3001/health

# Test authentication
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 📝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 🔒 Security

- Regular security updates
- Environment variable protection
- Database connection encryption
- Rate limiting and DDoS protection
- Input validation and sanitization

## 📞 Support

For issues and questions:
1. Check the logs: `docker-compose logs`
2. Verify database connection
3. Check environment variables
4. Review API documentation

---

**Built with ❤️ for Google Ads automation**
