import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Bo<PERSON>, BarChart2, <PERSON>ap, Code, Settings, Shield } from 'lucide-react';
import Header from '../components/Header';
import { useLanguage } from '../contexts/LanguageContext';

const ServicesPage: React.FC = () => {
  const { t } = useLanguage();

  const services = [
    {
      id: 1,
      icon: <BarChart2 className="w-8 h-8 text-blue-500" />,
      title: t('services.campaign.title'),
      description: t('services.campaign.description'),
      features: [t('services.campaign.feature1'), t('services.campaign.feature2'), t('services.campaign.feature3')],
    },
    {
      id: 2,
      icon: <Zap className="w-8 h-8 text-blue-500" />,
      title: t('services.performance.title'),
      description: t('services.performance.description'),
      features: [t('services.performance.feature1'), t('services.performance.feature2'), t('services.performance.feature3')],
    },
    {
      id: 3,
      icon: <Code className="w-8 h-8 text-blue-500" />,
      title: t('services.scripting.title'),
      description: t('services.scripting.description'),
      features: [t('services.scripting.feature1'), t('services.scripting.feature2'), t('services.scripting.feature3')],
    },
    {
      id: 4,
      icon: <Settings className="w-8 h-8 text-blue-500" />,
      title: t('services.technical.title'),
      description: t('services.technical.description'),
      features: [t('services.technical.feature1'), t('services.technical.feature2'), t('services.technical.feature3')],
    },
    {
      id: 5,
      icon: <Shield className="w-8 h-8 text-blue-500" />,
      title: t('services.security.title'),
      description: t('services.security.description'),
      features: [t('services.security.feature1'), t('services.security.feature2'), t('services.security.feature3')],
    },
  ];
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow bg-gradient-to-b from-gray-900 to-gray-800 text-white overflow-hidden">
        <div className="relative pt-28 pb-20 md:pt-36 md:pb-28">
          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-blue-900/20 to-transparent"></div>
            <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
          </div>
          
          {/* Hero section */}
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6">
                {t('services.page.title.part1')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">{t('services.page.title.part2')}</span>
              </h1>
              <p className="text-lg md:text-xl text-blue-100/90 max-w-3xl mx-auto">
                {t('services.page.subtitle')}
              </p>
            </div>

            {/* Services grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service) => (
                <div 
                  key={service.id}
                  className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="w-12 h-12 rounded-lg bg-blue-500/10 flex items-center justify-center mb-4">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">{service.title}</h3>
                  <p className="text-gray-300 mb-4">{service.description}</p>
                  <ul className="space-y-1.5">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-400">
                        <svg className="w-4 h-4 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* CTA section */}
            <div className="mt-20 text-center">
              <h2 className="text-2xl md:text-3xl font-bold mb-6">Ready to boost your advertising performance?</h2>
              <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
                Get in touch with our team to discuss how we can help you achieve your advertising goals.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link
                  to="/contact"
                  className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
                >
                  Contact Us
                </Link>
                <Link
                  to="/dashboard"
                  className="inline-flex items-center justify-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-white bg-transparent hover:bg-gray-700/50 transition-colors duration-200"
                >
                  Go to Dashboard
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ServicesPage;
