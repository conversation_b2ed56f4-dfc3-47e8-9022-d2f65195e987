import React, { useState, useEffect } from 'react';
import { Send, MessageCircle, Tag, Info, ListChecks } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import NotificationMessage from '../ui/shared/NotificationMessage';
import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';

const LOCAL_STORAGE_KEYS = {
  TOKEN: 'telegramTokensHistory_v2',
  CHAT_ID: 'telegramChatIdsHistory_v2',
  TAG: 'accountTagsHistory_v2',
  CURRENT_TOKEN: 'telegramCurrentToken_v1',
  CURRENT_CHAT_ID: 'telegramCurrentChatId_v1',
  CURRENT_TAG: 'telegramCurrentTag_v1',
};

const MAX_HISTORY_LENGTH = 5;

const TelegramScriptGeneratorTool: React.FC = () => {
  const { t } = useLanguage();
  const [telegramToken, setTelegramToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const [accountTag, setAccountTag] = useState('');

  const [tokenHistory, setTokenHistory] = useState<string[]>([]);
  const [chatIdHistory, setChatIdHistory] = useState<string[]>([]);
  const [tagHistory, setTagHistory] = useState<string[]>([]);

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showMessagePreview, setShowMessagePreview] = useState(false);
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' | 'info' | 'warning' } | null>(null);

  useEffect(() => {
    const loadHistory = (key: string, setter: React.Dispatch<React.SetStateAction<string[]>>) => {
      try {
        const storedHistory = localStorage.getItem(key);
        if (storedHistory) {
          setter(JSON.parse(storedHistory));
        }
      } catch (error) {
        console.error('Failed to load history from localStorage:', error);
        setter([]); // Fallback to empty array on error
      }
    };
    loadHistory(LOCAL_STORAGE_KEYS.TOKEN, setTokenHistory);
    loadHistory(LOCAL_STORAGE_KEYS.CHAT_ID, setChatIdHistory);
    loadHistory(LOCAL_STORAGE_KEYS.TAG, setTagHistory);

    // Load current input values
    const currentToken = localStorage.getItem(LOCAL_STORAGE_KEYS.CURRENT_TOKEN);
    if (currentToken) setTelegramToken(currentToken);
    const currentChatId = localStorage.getItem(LOCAL_STORAGE_KEYS.CURRENT_CHAT_ID);
    if (currentChatId) setTelegramChatId(currentChatId);
    const currentTag = localStorage.getItem(LOCAL_STORAGE_KEYS.CURRENT_TAG);
    if (currentTag) setAccountTag(currentTag);

  }, []);

  // Save current input values to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(LOCAL_STORAGE_KEYS.CURRENT_TOKEN, telegramToken);
  }, [telegramToken]);

  useEffect(() => {
    localStorage.setItem(LOCAL_STORAGE_KEYS.CURRENT_CHAT_ID, telegramChatId);
  }, [telegramChatId]);

  useEffect(() => {
    // Allow empty string to be saved to clear it from localStorage if user deletes the tag
    localStorage.setItem(LOCAL_STORAGE_KEYS.CURRENT_TAG, accountTag);
  }, [accountTag]);

  const handleInputChange = (
    setter: React.Dispatch<React.SetStateAction<string>>,
    fieldName: keyof typeof errors
  ) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setter(value);
    // Clear specific field error if it exists
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
    // Clear global message if user starts typing after a global error was shown
    if (message && message.type === 'error') {
      setMessage(null);
    }
  };

  const updateHistory = (value: string, key: string, currentHistory: string[], setter: React.Dispatch<React.SetStateAction<string[]>>) => {
    if (!value.trim()) return;
    try {
      const newHistory = [value, ...currentHistory.filter(item => item !== value)].slice(0, MAX_HISTORY_LENGTH);
      setter(newHistory);
      localStorage.setItem(key, JSON.stringify(newHistory));
    } catch (error) {
      console.error('Failed to update history in localStorage:', error);
    }
  };

  const generateUniqueScriptText = (token: string, chatId: string, tag: string): string => {
    const generateRandomString = (length = 0): string => {
      if (!length) length = Math.floor(Math.random() * 6) + 5;
      const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }
      return result;
    };

    const randomSuffix = () => {
      const adjectives = ['Fast', 'Brave', 'Active', 'Dynamic', 'Eager', 'Happy', 'Calm', 'Good', 'Wise', 'Keen'];
      const nouns = ['Bear', 'Fox', 'Dog', 'Cat', 'Eagle', 'Horse', 'Goat', 'Apple', 'Lion', 'Tiger'];
      const randomNum = Math.floor(Math.random() * 10000);
      return adjectives[Math.floor(Math.random() * adjectives.length)] +
             nouns[Math.floor(Math.random() * nouns.length)] +
             randomNum;
    };

    // Generate completely obfuscated variable names
    const tokenVarName = generateRandomString(8);
    const chatIdVarName = generateRandomString(7);
    const tagAccountsVarName = generateRandomString(6);

    // Variable names for today's metrics
    const accountVarName = generateRandomString(9);
    const dateVarName = generateRandomString(8);
    const impressionsVarName = generateRandomString(10);
    const clicksVarName = generateRandomString(7);
    const costVarName = generateRandomString(6);
    const conversionsVarName = generateRandomString(11);
    const ctrVarName = generateRandomString(5);
    const cpcVarName = generateRandomString(6);
    const convRateVarName = generateRandomString(9);
    const costPerConvVarName = generateRandomString(12);

    // Variable names for yesterday's metrics
    const yesterdayCostVarName = generateRandomString(13);
    const yesterdayClicksVarName = generateRandomString(14);
    const costChangeVarName = generateRandomString(10);
    const clicksChangeVarName = generateRandomString(11);

    // Variable names for last week's metrics
    const lastWeekCostVarName = generateRandomString(12);
    const lastWeekClicksVarName = generateRandomString(13);
    const weekCostChangeVarName = generateRandomString(14);
    const weekClicksChangeVarName = generateRandomString(15);

    // Variable names for budget tracking
    const totalBudgetVarName = generateRandomString(11);
    const budgetUtilVarName = generateRandomString(10);
    const budgetMsgVarName = generateRandomString(9);

    // Variable names for campaign performance
    const campaignsVarName = generateRandomString(9);
    const campaignIteratorVarName = generateRandomString(16);
    const campaignVarName = generateRandomString(8);
    const campaignStatsVarName = generateRandomString(13);
    const campaignMsgVarName = generateRandomString(11);

    // These variables are used in the generated script
    const msgVarName = generateRandomString(7);

    const mainFuncName = `main${randomSuffix()}`;
    const sendMsgFuncName = `send${randomSuffix()}`;

    const sanitizedTag = tag ? tag.replace(/[^a-zA-Z0-9_]/g, '') : '';
    const internalScript = `
function ${mainFuncName}() {
  var ${tokenVarName} = "${token}";
  var ${chatIdVarName} = "${chatId}";
  var ${tagAccountsVarName} = "${sanitizedTag}"; 
  var ${tagAccountsVarName}_hashtag = "${sanitizedTag.replace(/\s+/g, '_')}";

  try {
    var ${accountVarName} = AdsApp.currentAccount();
    var ${dateVarName}_instance = new Date();
    var formattedDateString = Utilities.formatDate(${dateVarName}_instance, ${accountVarName}.getTimeZone(), "yyyy-MM-dd");

    var ${impressionsVarName} = ${accountVarName}.getStatsFor("TODAY").getImpressions();
    var ${clicksVarName} = ${accountVarName}.getStatsFor("TODAY").getClicks();
    var ${costVarName} = ${accountVarName}.getStatsFor("TODAY").getCost();
    var ${conversionsVarName} = ${accountVarName}.getStatsFor("TODAY").getConversions();
    var ${ctrVarName} = ${accountVarName}.getStatsFor("TODAY").getCtr();
    var ${cpcVarName} = ${accountVarName}.getStatsFor("TODAY").getAverageCpc();
    var ${convRateVarName} = ${accountVarName}.getStatsFor("TODAY").getConversionRate();
    var ${costPerConvVarName} = ${conversionsVarName} > 0 ? (${costVarName} / ${conversionsVarName}) : 0;

    var ${yesterdayCostVarName} = ${accountVarName}.getStatsFor("YESTERDAY").getCost();
    var ${yesterdayClicksVarName} = ${accountVarName}.getStatsFor("YESTERDAY").getClicks();
    
    var ${costChangeVarName} = ${yesterdayCostVarName} > 0 ? 
        ((${costVarName} - ${yesterdayCostVarName}) / ${yesterdayCostVarName} * 100).toFixed(2) + '%' : 'N/A';
    var ${clicksChangeVarName} = ${yesterdayClicksVarName} > 0 ? 
        ((${clicksVarName} - ${yesterdayClicksVarName}) / ${yesterdayClicksVarName} * 100).toFixed(2) + '%' : 'N/A';

    var last7DaysStats = ${accountVarName}.getStatsFor("LAST_7_DAYS");
    var ${lastWeekCostVarName} = last7DaysStats.getCost() / 7;
    var ${lastWeekClicksVarName} = last7DaysStats.getClicks() / 7;
    
    var ${weekCostChangeVarName} = ${lastWeekCostVarName} > 0 ? 
        ((${costVarName} - ${lastWeekCostVarName}) / ${lastWeekCostVarName} * 100).toFixed(2) + '%' : 'N/A';
    var ${weekClicksChangeVarName} = ${lastWeekClicksVarName} > 0 ? 
        ((${clicksVarName} - ${lastWeekClicksVarName}) / ${lastWeekClicksVarName} * 100).toFixed(2) + '%' : 'N/A';

    var ${totalBudgetVarName} = 0;
    var enabledCampaignsIterator = AdsApp.campaigns().withCondition("Status = ENABLED").get();
    while (enabledCampaignsIterator.hasNext()) {
      var campaign = enabledCampaignsIterator.next();
      if (campaign.getBudget()) { 
         ${totalBudgetVarName} += campaign.getBudget().getAmount();
      }
    }

    var ${budgetUtilVarName} = ${totalBudgetVarName} > 0 ? (${costVarName} / ${totalBudgetVarName} * 100).toFixed(2) : 0;
    var ${budgetMsgVarName} = '';

    if (parseFloat(${budgetUtilVarName}) > 80) { 
        ${budgetMsgVarName} = '\\n\\n⚠️ *Budget Alert:* ' + ${budgetUtilVarName} + '% of daily budget used!';
    }

    var ${campaignsVarName} = [];
    var ${campaignIteratorVarName} = AdsApp.campaigns()
        .withCondition("Status = ENABLED")
        .withCondition("Impressions > 0") 
        .forDateRange("TODAY")
        .orderBy("Cost DESC")
        .withLimit(5)
        .get();

    while (${campaignIteratorVarName}.hasNext()) {
      var ${campaignVarName} = ${campaignIteratorVarName}.next();
      var ${campaignStatsVarName} = ${campaignVarName}.getStatsFor("TODAY");
      ${campaignsVarName}.push({
        name: ${campaignVarName}.getName(),
        cost: ${campaignStatsVarName}.getCost(),
        clicks: ${campaignStatsVarName}.getClicks(),
        impressions: ${campaignStatsVarName}.getImpressions(),
        conversions: ${campaignStatsVarName}.getConversions(),
        ctr: ${campaignStatsVarName}.getCtr(),
        status: ${campaignVarName}.getStatus()
      });
    }

    var ${campaignMsgVarName} = '';
    if (${campaignsVarName}.length > 0) {
      ${campaignMsgVarName} = '\\n\\n🔝 *Top Campaigns Today:*\\n';
      for (var i = 0; i < ${campaignsVarName}.length; i++) {
        var c = ${campaignsVarName}[i]; 
        ${campaignMsgVarName} += (i+1) + '. *' + c.name + '* ('+ c.status +')\\n' +
            '   💰 ' + c.cost.toFixed(2) + ' ' + ${accountVarName}.getCurrencyCode() + 
            ' | 🖱️ ' + c.clicks + 
            ' | 🎯 ' + (c.ctr * 100).toFixed(2) + '%';
        
        if (c.conversions > 0) {
          ${campaignMsgVarName} += ' | 🛒 ' + c.conversions.toFixed(0); 
        }
        ${campaignMsgVarName} += '\\n';
      }
    } else {
      ${campaignMsgVarName} = '\\n\\nNo campaign data with impressions available for today.';
    }

    var ${msgVarName} = '📊 *Performance Report* - ' + formattedDateString + '\\n' +
        '🏷️ *Account:* ' + ${accountVarName}.getCustomerId() + ' (' + (${tagAccountsVarName} || ${accountVarName}.getName()) + ')\\n' +
        '\\n' +
        "📈 *Today's Performance:*\\n" +
        '👁️ Impressions: ' + ${impressionsVarName}.toLocaleString() + ' | 🖱️ Clicks: ' + ${clicksVarName}.toLocaleString() + ' (DoD: ' + ${clicksChangeVarName} + ' | WoW: ' + ${weekClicksChangeVarName} + ')\\n' +
        '🎯 CTR: ' + (${ctrVarName} * 100).toFixed(2) + '% | 💰 CPC: ' + ${cpcVarName}.toFixed(2) + ' ' + ${accountVarName}.getCurrencyCode();

    if (${conversionsVarName} > 0) {
      ${msgVarName} += ' | 🛒 Conversions: ' + ${conversionsVarName}.toLocaleString() + '\\n' +
          '📋 Conv. Rate: ' + (${convRateVarName} * 100).toFixed(2) + '% | 💵 Cost/Conv: ' + ${costPerConvVarName}.toFixed(2) + ' ' + ${accountVarName}.getCurrencyCode() + '\\n';
    } else {
      ${msgVarName} += ' | 🛒 Conversions: 0\\n';
    }

    ${msgVarName} += '💸 Spent: ' + ${costVarName}.toLocaleString() + ' ' + ${accountVarName}.getCurrencyCode() +
        ' (DoD: ' + ${costChangeVarName} + ' | WoW: ' + ${weekCostChangeVarName} + ') | 💼 Budget Used: ' + ${budgetUtilVarName} + '%' +
        ${budgetMsgVarName} +
        ${campaignMsgVarName};
        
    if (${tagAccountsVarName}_hashtag) {
        ${msgVarName} += '\\n\\n#' + ${tagAccountsVarName}_hashtag;
    }

    ${sendMsgFuncName}(${tokenVarName}, ${chatIdVarName}, ${msgVarName});
  } catch (e) {
    Logger.log("Error in " + '${mainFuncName}' + ": " + e.toString());
    ${sendMsgFuncName}(${tokenVarName}, ${chatIdVarName}, "⚠️ Error in Google Ads Script: " + '${mainFuncName}' + "\\n" + e.toString().substring(0, 1000));
  }
}

function ${sendMsgFuncName}(token, chatId, text) {
  var telegramApiUrl = "https://api.telegram.org/bot" + token + "/sendMessage";
  var options = {
    "method": "post",
    "contentType": "application/json",
    "payload": JSON.stringify({
      "chat_id": chatId,
      "text": text,
      "parse_mode": "Markdown" 
    }),
    "muteHttpExceptions": true // Prevent exceptions for non-200 responses, so we can log them
  };
  try {
    var response = UrlFetchApp.fetch(telegramApiUrl, options);
    Logger.log('Telegram API Response Code: ' + response.getResponseCode());
    Logger.log('Telegram API Response Content: ' + response.getContentText());
  } catch (e) {
    Logger.log("Error sending message to Telegram or processing response: " + e.toString());
  }
}`;

    // Script is now just the main function wrapper, the internal script, and the call to the internal main function.
    return `function main() {
${internalScript}
  ${mainFuncName}();
}`;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const tokenPattern = /^\d{8,10}:[a-zA-Z0-9_-]{35}$/; // Basic token pattern
    const chatIdPattern = /^-?\d+$/; // Allows negative numbers for group/channel IDs

    if (!telegramToken.trim()) {
      newErrors.telegramToken = t('validation.required');
    } else if (!tokenPattern.test(telegramToken.trim())) {
      newErrors.telegramToken = t('validation.invalidFormat');
    }

    if (!telegramChatId.trim()) {
      newErrors.telegramChatId = t('validation.required');
    } else if (!chatIdPattern.test(telegramChatId.trim())) {
      newErrors.telegramChatId = t('validation.invalidFormat');
    }

    if (!accountTag.trim()) {
      newErrors.accountTag = t('validation.required');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null); // Clear previous messages
    
    if (!validateForm()) {
      setMessage({ text: 'Please correct the errors highlighted below.', type: 'error' });
      return;
    }
    
    // Only generate script and show result when submitting the form (Generate Script button)
    const newScript = generateUniqueScriptText(telegramToken.trim(), telegramChatId.trim(), accountTag.trim());
    setGeneratedScript(newScript);
    setShowResult(true);
    setShowMessagePreview(false); // Hide preview when showing script

    updateHistory(telegramToken.trim(), LOCAL_STORAGE_KEYS.TOKEN, tokenHistory, setTokenHistory);
    updateHistory(telegramChatId.trim(), LOCAL_STORAGE_KEYS.CHAT_ID, chatIdHistory, setChatIdHistory);
    updateHistory(accountTag.trim(), LOCAL_STORAGE_KEYS.TAG, tagHistory, setTagHistory);
    setMessage({ text: t('success.scriptGenerated'), type: 'success' });
  };

  const handleBackToForm = () => {
    setShowResult(false);
    setGeneratedScript(''); // Clear script
    setMessage(null); // Clear message
    setErrors({}); // Clear errors
  };

  return (
    <ToolPageLayout
      title={t('telegram.title') || 'Telegram Script Generator'}
      description={t('telegram.description') || 'Generate Google Ads scripts for Telegram notifications'}
    >
      {/* Display global messages here */}
      {message && (
        <div className="my-4">
          <NotificationMessage 
            message={message.text} 
            type={message.type} 
            onDismiss={() => setMessage(null)} 
          />
        </div>
      )}

      {!showResult ? (
        <form onSubmit={handleSubmit} className="space-y-6 mt-4">
          <FormSection
            title={t('telegram.bot_config') || 'Telegram Bot Configuration'}
            icon={<MessageCircle size={22} className="mr-2" />}
            theme="blue"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <FormItem
                label={t('telegram.bot_token') || 'Bot Token'}
                htmlFor="telegramToken"
                tooltipText={t('tooltip.botToken')}
              >
                <StyledInput 
                  type="text" 
                  id="telegramToken" 
                  value={telegramToken}
                  onChange={handleInputChange(setTelegramToken, 'telegramToken')}
                  placeholder={t('telegram.bot_token_placeholder') || 'Your Telegram Bot Token'}
                  required 
                  list="telegramTokenDatalist"
                  error={!!errors.telegramToken}
                />
                {errors.telegramToken && <p className="mt-1 text-xs text-red-400">{errors.telegramToken}</p>}
                <datalist id="telegramTokenDatalist">
                  {tokenHistory.map((item, index) => (
                    <option key={`${LOCAL_STORAGE_KEYS.TOKEN}-${index}`} value={item} />
                  ))}
                </datalist>
              </FormItem>

              <FormItem
                label={t('telegram.chat_id') || 'Chat ID'}
                htmlFor="telegramChatId"
                tooltipText={t('tooltip.chatId')}
              >
                <StyledInput
                  type="text"
                  id="telegramChatId"
                  value={telegramChatId}
                  onChange={handleInputChange(setTelegramChatId, 'telegramChatId')}
                  placeholder={t('telegram.chat_id_placeholder') || 'Your Telegram Chat ID'}
                  required 
                  list="telegramChatIdDatalist"
                  error={!!errors.telegramChatId}
                />
                {errors.telegramChatId && <p className="mt-1 text-xs text-red-400">{errors.telegramChatId}</p>}
                <datalist id="telegramChatIdDatalist">
                  {chatIdHistory.map((item, index) => (
                    <option key={`${LOCAL_STORAGE_KEYS.CHAT_ID}-${index}`} value={item} />
                  ))}
                </datalist>
              </FormItem>
            </div>
            
            <div className="mt-4">
              <details className="group">
                <summary className="flex items-center text-sm font-medium text-blue-400 hover:text-blue-300 cursor-pointer">
                  <Info size={16} className="mr-1.5" />
                  How to get Bot Token and Chat/Group ID
                  <svg className="ml-1.5 w-4 h-4 transform group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </summary>
                <div className="mt-2 p-4 bg-gray-800/50 rounded-lg text-sm text-gray-300 space-y-4">
                  <div>
                    <h4 className="font-semibold text-blue-300 mb-1">Getting a Bot Token:</h4>
                    <ol className="list-decimal list-inside space-y-1 pl-2">
                      <li>Open Telegram and search for <span className="font-mono bg-gray-700 px-1.5 py-0.5 rounded">@BotFather</span></li>
                      <li>Start a chat and send: <span className="font-mono bg-gray-700 px-1.5 py-0.5 rounded">/newbot</span></li>
                      <li>Follow the instructions to name your bot and choose a username</li>
                      <li>After creation, you'll receive a token in the format: <span className="font-mono bg-gray-700 px-1.5 py-0.5 rounded">1234567890:AAHEsM-ExampleTokenHere123</span></li>
                    </ol>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-blue-300 mb-1">Getting a Chat/Group ID:</h4>
                    <p className="mb-2">For personal chats or groups:</p>
                    <ol className="list-decimal list-inside space-y-1 pl-2">
                      <li>Add <span className="font-mono bg-gray-700 px-1.5 py-0.5 rounded">@userinfobot</span> to your chat/group</li>
                      <li>Send any message to the chat/group</li>
                      <li>The bot will reply with the Chat ID</li>
                    </ol>
                    
                    <p className="mt-3 mb-2">For channels:</p>
                    <ol className="list-decimal list-inside space-y-1 pl-2">
                      <li>Forward a message from your channel to <span className="font-mono bg-gray-700 px-1.5 py-0.5 rounded">@username_to_id_bot</span></li>
                      <li>The bot will reply with the Channel ID (starts with <span className="font-mono bg-gray-700 px-1.5 py-0.5 rounded">-100</span>)</li>
                    </ol>
                    
                    <p className="mt-3 text-yellow-300 text-xs">Note: For groups and channels, your bot must be an admin to send messages.</p>
                  </div>
                </div>
              </details>
            </div>
          </FormSection>

          <FormSection
            title={t('telegram.account_identification') || 'Account Identification'}
            icon={<Tag size={22} className="mr-2" />}
            theme="purple"
          >
            <FormItem
              label={t('telegram.account_tag') || 'Account Tag'}
              htmlFor="accountTag"
              tooltipText={t('tooltip.accountTag')}
            >
              <StyledInput
                type="text"
                id="accountTag"
                value={accountTag}
                onChange={handleInputChange(setAccountTag, 'accountTag')}
                placeholder={t('telegram.account_tag_placeholder') || 'Enter account identifier'}
                list="accountTagDatalist"
                error={!!errors.accountTag}
              />
              {errors.accountTag && <p className="mt-1 text-xs text-red-400">{errors.accountTag}</p>}
              <datalist id="accountTagDatalist">
                {tagHistory.map((item, index) => (
                  <option key={`${LOCAL_STORAGE_KEYS.TAG}-${index}`} value={item} />
                ))}
              </datalist>
            </FormItem>
          </FormSection>

          {/* Preview Message Section - This doesn't use FormSection as it's a display toggle */}
          <div className="bg-sky-50 p-5 rounded-lg shadow-inner border-2 border-sky-200 mt-6 mb-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-sky-700 flex items-center">
                <ListChecks size={20} className="mr-2 text-sky-600" />
                Message Content Preview Options
              </h3>
              <StyledButton 
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowMessagePreview(!showMessagePreview);
                }} 
                variant={showMessagePreview ? "outline" : "secondary"} 
                size="md"
              >
                {showMessagePreview ? 'Hide Preview' : 'Show Preview'}
              </StyledButton>
            </div>
            
            {showMessagePreview && (
              <div className="mt-4">
                <div className="bg-[#1E2429] text-white p-4 rounded-lg shadow-inner border border-gray-700 font-mono text-sm overflow-auto max-h-[400px]">
                  <div className="flex items-start mb-2">
                    <div className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-2 flex-shrink-0">
                      <span className="font-bold">B</span>
                    </div>
                    <div className="flex-1">
                      <div className="font-bold">Your Bot</div>
                    </div>
                  </div>
                  <div className="pl-10 whitespace-pre-line">
                    <p className="font-bold mb-1">📊 *Performance Report* - {new Date().toISOString().split('T')[0]}</p>
                    <p className="mb-1">🏷️ *Account:* 123-456-7890 ({accountTag || 'Your Account Tag'})</p>
                    <p className="font-bold mb-1">📈 *Today's Performance:*</p>
                    <p className="mb-1">👁️ Impressions: 1,234 | 🖱️ Clicks: 56 (DoD: +12.5% | WoW: +8.2%)</p>
                    <p className="mb-1">🎯 CTR: 4.54% | 💰 CPC: 0.75 USD | 🛒 Conversions: 3</p>
                    <p className="mb-1">📋 Conv. Rate: 5.36% | 💵 Cost/Conv: 14.00 USD</p>
                    <p className="mb-1">💸 Spent: 42.00 USD (DoD: +15.2% | WoW: +5.4%) | 💼 Budget Used: 65.3%</p>
                    <p className="mb-1"></p>
                    <p className="font-bold mb-1">🔝 *Top Campaigns Today:*</p>
                    <p className="mb-1">1. *Brand Campaign* (ENABLED) | 💰 18.50 USD | 🖱️ 25 | 🎯 5.62% | 🛒 2</p>
                    <p className="mb-1">2. *Generic Keywords* (ENABLED) | 💰 15.75 USD | 🖱️ 18 | 🎯 3.85% | 🛒 1</p>
                    <p className="mb-1">3. *Display Remarketing* (ENABLED) | 💰 7.75 USD | 🖱️ 13 | 🎯 2.15%</p>
                    <p>#{accountTag ? accountTag.replace(/[^a-zA-Z0-9_\s]/g, '').replace(/\s+/g, '_') : 'YourAccountTag'}</p>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">* This is a sample preview. Actual message will contain real-time data from your Google Ads account.</p>
              </div>
            )}
          </div>

          {Object.keys(errors).length > 0 && (
            <div className="p-3 my-4 text-sm rounded-lg bg-red-100 border border-red-400 text-red-700">
              Please fix the errors in the form before generating the script.
            </div>
          )}

          <StyledButton 
            type="submit"
            variant="primary"
            className="w-full"
            size="lg"
          >
            <Send size={18} className="mr-2" />
            {t('telegram.generate_button') || 'Generate Script'}
          </StyledButton>
        </form>
      ) : (
        <div id="resultSectionTelegramScript">
          <ScriptDisplay 
            scriptContent={generatedScript} 
            language="javascript" 
            onBack={() => {
              setShowResult(false);
              setGeneratedScript('');
            }}
          />
          
          <div className="mt-8 p-5 bg-yellow-50 rounded-lg shadow-inner border-2 border-yellow-200">
            <h3 className="text-lg font-semibold text-yellow-700 mb-3 flex items-center">
              <Info size={20} className="mr-2 text-yellow-600" />
              Setup and Instructions
            </h3>

            <div className="mb-6">
              <h4 className="font-semibold text-yellow-800 mb-2">How to Use This Script:</h4>
              <ol className="list-decimal list-inside space-y-2 text-yellow-800">
                <li>In Google Ads, go to Tools &amp; Settings &gt; Bulk Actions &gt; Scripts</li>
                <li>Click the "+" button to create a new script</li>
                <li>Paste the script above and give it a name (e.g., "Telegram Daily Report")</li>
                <li>Click "Preview" to test the script (check the Logs for any errors)</li>
                <li>If successful, click "Save" and set up a schedule (recommended: daily)</li>
              </ol>
            </div>

            <div className="mb-6">
              <h4 className="font-semibold text-yellow-800 mb-2">How to Create a Telegram Channel:</h4>
              <ol className="list-decimal list-inside space-y-2 text-yellow-800 text-sm">
                <li>Open Telegram app on your phone or desktop</li>
                <li>Tap the "New Message" button (pencil icon)</li>
                <li>Select "New Channel" from the menu</li>
                <li>Enter channel name and description (optional)</li>
                <li>Choose channel type:
                  <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                    <li><strong>Public:</strong> Anyone can find and join via link</li>
                    <li><strong>Private:</strong> Only people with invite link can join</li>
                  </ul>
                </li>
                <li>Add your bot as an administrator to the channel</li>
                <li>Give the bot permission to "Post Messages"</li>
              </ol>
            </div>

            <div>
              <h4 className="font-semibold text-yellow-800 mb-2">How to Find Channel ID:</h4>

              <div className="mb-4">
                <h5 className="font-medium text-yellow-700 mb-1">For Public Channels:</h5>
                <ol className="list-decimal list-inside space-y-1 text-yellow-800 text-sm ml-2">
                  <li>Use format: <code className="bg-yellow-200 px-1 py-0.5 rounded">@channelname</code> (e.g., @myreports)</li>
                  <li>Or get numeric ID using a bot like @userinfobot</li>
                </ol>
              </div>

              <div>
                <h5 className="font-medium text-yellow-700 mb-1">For Private Channels:</h5>
                <ol className="list-decimal list-inside space-y-1 text-yellow-800 text-sm ml-2">
                  <li>Add bot @userinfobot to your channel as admin</li>
                  <li>Forward any message from the channel to @userinfobot</li>
                  <li>The bot will reply with channel details including the numeric ID</li>
                  <li>Use the numeric ID (starts with -100, e.g., -1001234567890)</li>
                  <li>Remove @userinfobot from channel after getting the ID</li>
                </ol>
              </div>

              <p className="text-xs text-yellow-600 mt-3 bg-yellow-100 p-2 rounded">
                <strong>Note:</strong> Channel IDs are negative numbers starting with -100. Group chat IDs are also negative but start with just -. Personal chat IDs are positive numbers.
              </p>
            </div>
          </div>

          <StyledButton 
            onClick={handleBackToForm}
            variant="primary"
            size="lg"
            className="mt-8 w-full"
          >
            {t('common.generate')}
          </StyledButton>
        </div>
      )}
    </ToolPageLayout>

);
};

export default TelegramScriptGeneratorTool;
