import React, { useState, useEffect } from 'react';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Bell, DollarSign, CalendarDays, FileText, Settings, Info } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

// Ambient declarations for Google Ads Script global objects (assuming they are needed, as in other tools)
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var AdsApp: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var Logger: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var Utilities: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var UrlFetchApp: any; // For Telegram

const LOCAL_STORAGE_PREFIX = 'searchQueryPerformance_';

const getGoogleAdsDateRanges = (t: (key: string) => string) => [
  { value: 'TODAY', label: t('common.dateRanges.today') },
  { value: 'YESTERDAY', label: t('common.dateRanges.yesterday') },
  { value: 'LAST_7_DAYS', label: t('common.dateRanges.last7Days') },
  { value: 'LAST_WEEK_SUN_SAT', label: t('common.dateRanges.lastWeekSunSat') },
  { value: 'LAST_BUSINESS_WEEK', label: t('common.dateRanges.lastBusinessWeek') },
  { value: 'THIS_MONTH', label: t('common.dateRanges.thisMonth') },
  { value: 'LAST_MONTH', label: t('common.dateRanges.lastMonth') },
  { value: 'LAST_14_DAYS', label: t('common.dateRanges.last14Days') },
  { value: 'LAST_30_DAYS', label: t('common.dateRanges.last30Days') },
  { value: 'LAST_90_DAYS', label: t('common.dateRanges.last90Days') },
  { value: 'THIS_WEEK_SUN_TODAY', label: t('common.dateRanges.thisWeekSunToday') },
  { value: 'THIS_WEEK_MON_TODAY', label: t('common.dateRanges.thisWeekMonToday') },
  { value: 'LAST_WEEK_MON_SUN', label: t('common.dateRanges.lastWeekMonSun') },
];

const SearchQueryPerformance: React.FC = () => {
  const { t } = useLanguage();

  // Form inputs
  const [campaignNamePattern, setCampaignNamePattern] = useState('');
  const [cpcThreshold, setCpcThreshold] = useState('5.0');
  const [minImpressions, setMinImpressions] = useState('100');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [showTelegramSettings, setShowTelegramSettings] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  useEffect(() => {
    const fields = [
      { key: 'campaignNamePattern', setter: setCampaignNamePattern, type: 'string' },
      { key: 'cpcThreshold', setter: setCpcThreshold, type: 'string' },
      { key: 'minImpressions', setter: setMinImpressions, type: 'string' },
      { key: 'dateRange', setter: setDateRange, type: 'string' },
      { key: 'useTelegram', setter: setUseTelegram, type: 'boolean' },
      { key: 'telegramBotToken', setter: setTelegramBotToken, type: 'string' },
      { key: 'telegramChatId', setter: setTelegramChatId, type: 'string' },
    ];

    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field.key}`);
      if (savedValue !== null) {
        if (field.type === 'boolean') {
          const booleanValue = savedValue === 'true';
          (field.setter as React.Dispatch<React.SetStateAction<boolean>>)(booleanValue);
          if (field.key === 'useTelegram') {
            setShowTelegramSettings(booleanValue);
          }
        } else {
          (field.setter as React.Dispatch<React.SetStateAction<string>>)(savedValue);
        }
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const target = e.target as HTMLInputElement;
    const isCheckbox = type === 'checkbox';
    const valToSet = isCheckbox ? target.checked : value;

    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(valToSet));

    if (name === 'campaignNamePattern') {
      setCampaignNamePattern(valToSet as string);
    } else if (name === 'cpcThreshold') {
      setCpcThreshold(valToSet as string);
    } else if (name === 'minImpressions') {
      setMinImpressions(valToSet as string);
    } else if (name === 'dateRange') {
      setDateRange(valToSet as string);
    } else if (name === 'useTelegram') {
      const booleanVal = valToSet as boolean;
      setUseTelegram(booleanVal);
      setShowTelegramSettings(booleanVal);
      if (!booleanVal) {
        setTelegramBotToken('');
        setTelegramChatId('');
        localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramBotToken`);
        localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramChatId`);
      }
    } else if (name === 'telegramBotToken') {
      setTelegramBotToken(valToSet as string);
    } else if (name === 'telegramChatId') {
      setTelegramChatId(valToSet as string);
    }
  };

  const handleGenerateScript = () => {
    setMessage(null);
    if (useTelegram && (!telegramBotToken.trim() || !telegramChatId.trim())) {
      setMessage({ text: t('tools.searchQuery.errors.telegramRequired'), type: 'error' });
      return;
    }
    if (isNaN(parseFloat(cpcThreshold)) || parseFloat(cpcThreshold) <= 0) {
        setMessage({ text: t('tools.searchQuery.errors.invalidCpcThreshold'), type: 'error' });
        return;
    }
    if (isNaN(parseInt(minImpressions)) || parseInt(minImpressions) < 0) {
        setMessage({ text: t('tools.searchQuery.errors.invalidMinImpressions'), type: 'error' });
        return;
    }

    const script = generateSearchQueryScript(
      campaignNamePattern,
      parseFloat(cpcThreshold),
      parseInt(minImpressions),
      dateRange,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('resultSectionSearchQueryPerf')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const generateSearchQueryScript = (
    campaignPattern: string, cpcTh: number, minImp: number, dateRangeVal: string, 
    useTelegramVal: boolean, botToken: string, chatId: string
  ): string => {
    const uniquePrefix = 'sqPerf' + Date.now().toString(36) + '_';
    let telegramCode = '';
    if (useTelegramVal) {
      telegramCode = `
  function ${uniquePrefix}sendTelegramNotification(message) {
    var ${uniquePrefix}payload = {
      'chat_id': '${chatId.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}',
      'text': message,
      'parse_mode': 'HTML'
    };
    var ${uniquePrefix}options = {
      'method': 'post',
      'contentType': 'application/json',
      'payload': JSON.stringify(${uniquePrefix}payload)
    };
    try {
      UrlFetchApp.fetch('https://api.telegram.org/bot${botToken.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}/sendMessage', ${uniquePrefix}options);
      Logger.log('Telegram notification sent.');
    } catch (e) { Logger.log('Telegram Error: ' + e); }
  }
`;
    }

    // Function to convert date range constants to proper AWQL format
    const mainScript = `
  // Helper function to get the proper date range format for AWQL queries
  function getDuringClause(dateRange) {
    var today = new Date();
    var startDate, endDate;
    
    if (dateRange === 'TODAY') {
      startDate = endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'YESTERDAY') {
      var yesterday = new Date(today.getTime() - 24 * 3600 * 1000);
      startDate = endDate = Utilities.formatDate(yesterday, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'LAST_7_DAYS') {
      endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      var sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 3600 * 1000);
      startDate = Utilities.formatDate(sevenDaysAgo, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'LAST_14_DAYS') {
      endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      var fourteenDaysAgo = new Date(today.getTime() - 14 * 24 * 3600 * 1000);
      startDate = Utilities.formatDate(fourteenDaysAgo, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'LAST_30_DAYS') {
      endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      var thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 3600 * 1000);
      startDate = Utilities.formatDate(thirtyDaysAgo, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'LAST_90_DAYS') {
      endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      var ninetyDaysAgo = new Date(today.getTime() - 90 * 24 * 3600 * 1000);
      startDate = Utilities.formatDate(ninetyDaysAgo, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'THIS_MONTH') {
      var firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      startDate = Utilities.formatDate(firstDayOfMonth, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else if (dateRange === 'LAST_MONTH') {
      var firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      var lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      startDate = Utilities.formatDate(firstDayOfLastMonth, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      endDate = Utilities.formatDate(lastDayOfLastMonth, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    } else {
      // Default to last 30 days if the date range is not recognized
      endDate = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
      var thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 3600 * 1000);
      startDate = Utilities.formatDate(thirtyDaysAgo, AdsApp.currentAccount().getTimeZone(), 'yyyyMMdd');
    }
    
    return startDate + ',' + endDate;
  }

function main() {
  var ${uniquePrefix}CONFIG = {
    CAMPAIGN_NAME_CONTAINS: "${campaignPattern.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}", // Leave empty to target all campaigns
    CPC_THRESHOLD: ${cpcTh}, // Queries with CPC > this are flagged as high CPC
    MIN_IMPRESSIONS: ${minImp}, // Minimum impressions for a query to be analyzed
    DATE_RANGE: "${dateRangeVal}",
    LOW_CPC_FACTOR: 0.5 // Queries with CPC < (CPC_THRESHOLD * LOW_CPC_FACTOR) are flagged as low CPC
  };

  var ${uniquePrefix}highCpcQueries = [];
  var ${uniquePrefix}lowCpcQueries = [];
  var ${uniquePrefix}logMessages = [];
  var ${uniquePrefix}accountTimezone = AdsApp.currentAccount().getTimeZone();
  var ${uniquePrefix}formattedTime = Utilities.formatDate(new Date(), ${uniquePrefix}accountTimezone, 'yyyy-MM-dd HH:mm:ss');
  ${uniquePrefix}logMessages.push('Search Query Analyzer Script Started at: ' + ${uniquePrefix}formattedTime);

  var ${uniquePrefix}campaignSelector = AdsApp.campaigns().withCondition('Status = ENABLED').withCondition('AdvertisingChannelType = SEARCH');
  if (${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS !== "") {
    ${uniquePrefix}campaignSelector = ${uniquePrefix}campaignSelector.withCondition("Name CONTAINS_IGNORE_CASE '" + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + "'");
  }
  var ${uniquePrefix}campaignIterator = ${uniquePrefix}campaignSelector.get();

  ${uniquePrefix}logMessages.push('Iterating through campaigns...');
  while (${uniquePrefix}campaignIterator.hasNext()) {
    var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
    ${uniquePrefix}logMessages.push('Processing Campaign: ' + ${uniquePrefix}campaign.getName());

    var ${uniquePrefix}report = AdsApp.report(
      'SELECT Query, Clicks, Cost, Impressions, CampaignName, AdGroupName ' +
      'FROM SEARCH_QUERY_PERFORMANCE_REPORT ' +
      'WHERE CampaignId = ' + ${uniquePrefix}campaign.getId() + ' ' +
      'AND Impressions >= ' + ${uniquePrefix}CONFIG.MIN_IMPRESSIONS + ' ' +
      'AND Clicks > 0 ' + // Ensure there are clicks to calculate CPC
      "DURING " + getDuringClause(${uniquePrefix}CONFIG.DATE_RANGE)
    );

    var ${uniquePrefix}rows = ${uniquePrefix}report.rows();
    if (!${uniquePrefix}rows.hasNext()){
        ${uniquePrefix}logMessages.push('  No search queries found matching criteria in this campaign.');
    }
    while (${uniquePrefix}rows.hasNext()) {
      var ${uniquePrefix}row = ${uniquePrefix}rows.next();
      var ${uniquePrefix}query = ${uniquePrefix}row['Query'];
      var ${uniquePrefix}clicks = parseFloat(${uniquePrefix}row['Clicks']);
      var ${uniquePrefix}cost = parseFloat(${uniquePrefix}row['Cost']);
      var ${uniquePrefix}impressions = parseInt(${uniquePrefix}row['Impressions']);
      var ${uniquePrefix}campaignName = ${uniquePrefix}row['CampaignName'];
      var ${uniquePrefix}adGroupName = ${uniquePrefix}row['AdGroupName'];
      
      var ${uniquePrefix}cpc = (${uniquePrefix}clicks > 0) ? (${uniquePrefix}cost / ${uniquePrefix}clicks) : 0;
      var ${uniquePrefix}queryIdentifier = 'Campaign: "' + ${uniquePrefix}campaignName + '", Ad Group: "' + ${uniquePrefix}adGroupName + '", Query: "' + ${uniquePrefix}query + '"';

      ${uniquePrefix}logMessages.push('  Analyzing Query: ' + ${uniquePrefix}query + ' | Clicks: ' + ${uniquePrefix}clicks + ' | Cost: ' + ${uniquePrefix}cost.toFixed(2) + ' | CPC: ' + ${uniquePrefix}cpc.toFixed(2) + ' | Impr.: ' + ${uniquePrefix}impressions);

      if (${uniquePrefix}cpc > ${uniquePrefix}CONFIG.CPC_THRESHOLD) {
        var ${uniquePrefix}highCpcEntry = ${uniquePrefix}queryIdentifier + ' | CPC: ' + ${uniquePrefix}cpc.toFixed(2);
        ${uniquePrefix}highCpcQueries.push(${uniquePrefix}highCpcEntry);
        ${uniquePrefix}logMessages.push('    FLAGGED HIGH CPC: ' + ${uniquePrefix}highCpcEntry);
      } else if (${uniquePrefix}cpc < (${uniquePrefix}CONFIG.CPC_THRESHOLD * ${uniquePrefix}CONFIG.LOW_CPC_FACTOR)) {
        var ${uniquePrefix}lowCpcEntry = ${uniquePrefix}queryIdentifier + ' | CPC: ' + ${uniquePrefix}cpc.toFixed(2);
        ${uniquePrefix}lowCpcQueries.push(${uniquePrefix}lowCpcEntry);
        ${uniquePrefix}logMessages.push('    FLAGGED LOW CPC: ' + ${uniquePrefix}lowCpcEntry);
      }
    }
  }

  var ${uniquePrefix}summaryParts = ['Search Query Analyzer Run (' + ${uniquePrefix}formattedTime + '):'];
  
  if (${uniquePrefix}highCpcQueries.length > 0) {
    ${uniquePrefix}summaryParts.push('--- HIGH CPC QUERIES (Consider as Negative Keywords) ---');
    ${uniquePrefix}highCpcQueries.forEach(function(q) { ${uniquePrefix}summaryParts.push(q); });
  } else {
    ${uniquePrefix}summaryParts.push('No high CPC queries found meeting the criteria.');
  }

  if (${uniquePrefix}lowCpcQueries.length > 0) {
    ${uniquePrefix}summaryParts.push('--- LOW CPC QUERIES (Consider for Positive Keywords) ---');
    ${uniquePrefix}lowCpcQueries.forEach(function(q) { ${uniquePrefix}summaryParts.push(q); });
  } else {
    ${uniquePrefix}summaryParts.push('No low CPC queries found meeting the criteria.');
  }

  var ${uniquePrefix}summaryMessage = ${uniquePrefix}summaryParts.join('\\n'); // Use escaped newline character
  Logger.log('--- Summary ---');
  Logger.log(${uniquePrefix}summaryMessage);
  Logger.log('--- End of Summary ---');

  // Logger.log('--- Full Log --- ' + ${uniquePrefix}logMessages.join(' ') + ' --- End of Full Log ---'); // Uncomment for detailed logs

  // Create a formatted Telegram message with emojis and better formatting
  if (${useTelegramVal}) {
    var ${uniquePrefix}accountInfo = AdsApp.currentAccount();
    var ${uniquePrefix}accountName = ${uniquePrefix}accountInfo.getName();
    var ${uniquePrefix}accountId = ${uniquePrefix}accountInfo.getCustomerId();
    var ${uniquePrefix}totalFlagged = ${uniquePrefix}highCpcQueries.length + ${uniquePrefix}lowCpcQueries.length;
    
    var ${uniquePrefix}telegramMessage = '\\u2705 Script Execution Finished Successfully\\n' +
      '------------------------------------\\n' +
      '\\ud83d\\udcca Script: Search Query Performance Analyzer\\n' +
      '\\ud83c\\udfed Account: ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')\\n' +
      '\\ud83d\\udcc5 Date: ' + ${uniquePrefix}formattedTime + '\\n' +
      '\\ud83d\\udcc8 Results: ' + ${uniquePrefix}totalFlagged + ' search queries flagged for review\\n' +
      '------------------------------------';
      
    if (${uniquePrefix}highCpcQueries.length > 0) {
      ${uniquePrefix}telegramMessage += '\\n\\n\\u26A0 HIGH CPC QUERIES: ' + ${uniquePrefix}highCpcQueries.length;
    }
    
    if (${uniquePrefix}lowCpcQueries.length > 0) {
      ${uniquePrefix}telegramMessage += '\\n\\n\\ud83d\\udcb0 LOW CPC QUERIES: ' + ${uniquePrefix}lowCpcQueries.length;
    }
    
    ${uniquePrefix}sendTelegramNotification(${uniquePrefix}telegramMessage);
  }
  
  Logger.log('Search Query Analyzer Script Finished.');
}
`;
    return `${telegramCode}\n${mainScript}`;
  };

  return (
    <TooltipProvider>
      <ToolPageLayout
        title={t('tools.searchQuery.title')}
        description={t('tools.searchQuery.description')}
      >
        <div className="space-y-6">
          <FormSection title={t('tools.searchQuery.sections.parameters')} icon={<Info className="h-5 w-5" />} theme="slate">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('tools.searchQuery.parameters.note')}
            </p>
          </FormSection>

          <FormSection title={t('tools.searchQuery.sections.targeting')} icon={<Settings className="h-5 w-5" />} theme="sky">
            <FormItem label={t('tools.searchQuery.fields.campaignPattern')} htmlFor="campaignNamePattern" tooltipText={t('tools.searchQuery.fields.campaignPattern')}>
              <StyledInput
                id="campaignNamePattern"
                name="campaignNamePattern"
                value={campaignNamePattern}
                onChange={handleInputChange}
                placeholder={t('tools.searchQuery.fields.campaignPatternPlaceholder')}
              />
            </FormItem>
          </FormSection>

          <FormSection title={t('tools.searchQuery.sections.thresholds')} icon={<DollarSign className="h-5 w-5" />} theme="amber">
            <FormItem label={t('tools.searchQuery.fields.cpcThreshold')} htmlFor="cpcThreshold" tooltipText={t('tools.searchQuery.fields.cpcThreshold')}>
              <StyledInput
                id="cpcThreshold"
                name="cpcThreshold"
                type="number"
                value={cpcThreshold}
                onChange={handleInputChange}
                placeholder={t('tools.searchQuery.fields.cpcThresholdPlaceholder')}
                min="0.01"
                step="0.01"
              />
            </FormItem>
            <FormItem label={t('tools.searchQuery.fields.minImpressions')} htmlFor="minImpressions" tooltipText={t('tools.searchQuery.fields.minImpressions')}>
              <StyledInput
                id="minImpressions"
                name="minImpressions"
                type="number"
                value={minImpressions}
                onChange={handleInputChange}
                placeholder={t('tools.searchQuery.fields.minImpressionsPlaceholder')}
                min="0"
              />
            </FormItem>
          </FormSection>

          <FormSection title={t('tools.searchQuery.sections.period')} icon={<CalendarDays className="h-5 w-5" />} theme="emerald">
            <FormItem label={t('tools.searchQuery.fields.dateRange')} htmlFor="dateRange" tooltipText={t('tools.searchQuery.fields.dateRange')}>
              <select
                id="dateRange"
                name="dateRange"
                value={dateRange}
                onChange={handleInputChange}
                className="form-input block w-full rounded-md bg-slate-700/50 border-slate-600 text-gray-100 placeholder-gray-400 shadow-sm transition-colors duration-150 ease-in-out focus:border-transparent focus:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-sky-500 border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-2 text-sm"
                style={{ 
                  appearance: 'none',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'right 0.75rem center',
                  backgroundSize: '0.8em 0.8em',
                  paddingRight: '2.5rem',
                  backgroundImage: "url('data:image/svg+xml;charset=UTF-8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2212%22 height=%2212%22 viewBox=%220 0 12 12%22 fill=%22none%22%3E%3Cpath d=%22M2.25 4.5L6 8.25L9.75 4.5%22 stroke=%22%23a0aec0%22 stroke-width=%221.5%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22/%3E%3C/svg%3E')"
                }
              }>
                {getGoogleAdsDateRanges(t).map(range => (
                  <option key={range.value} value={range.value}>{range.label}</option>
                ))}
              </select>
            </FormItem>
          </FormSection>

          <FormSection title={t('tools.searchQuery.sections.notifications')} icon={<Bell className="h-5 w-5" />} theme="purple">
            <div className="mb-4">
              <label className="flex items-center cursor-pointer group">
                <div className="relative">
                  <input
                    type="checkbox"
                    id="useTelegram"
                    name="useTelegram"
                    checked={useTelegram}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`
                    w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                    ${useTelegram 
                      ? 'bg-sky-600 border-sky-600' 
                      : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                    }`}
                  >
                    {useTelegram && (
                      <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </div>
                <span className="ml-3 text-gray-300 text-sm font-medium">
                  {t('common.enableTelegram')}
                </span>
              </label>
            </div>
            {showTelegramSettings && (
              <>
                <FormItem label={t('tools.searchQuery.fields.telegramToken')} htmlFor="telegramBotToken" tooltipText={t('tools.searchQuery.fields.telegramToken')}>
                  <StyledInput
                    id="telegramBotToken"
                    name="telegramBotToken"
                    value={telegramBotToken}
                    onChange={handleInputChange}
                    placeholder={t('common.telegramTokenPlaceholder')}
                    type="password"
                  />
                </FormItem>
                <FormItem label={t('tools.searchQuery.fields.telegramChatId')} htmlFor="telegramChatId" tooltipText={t('tools.searchQuery.fields.telegramChatId')}>
                  <StyledInput
                    id="telegramChatId"
                    name="telegramChatId"
                    value={telegramChatId}
                    onChange={handleInputChange}
                    placeholder={t('common.telegramChatIdPlaceholder')}
                  />
                </FormItem>
              </>
            )}
          </FormSection>

          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-2">
             <StyledButton 
                onClick={handleGenerateScript} 
                leftIcon={<FileText className="h-5 w-5" />} 
                className="w-full sm:w-auto"
                aria-label="Generate Google Ads Script"
             >
                {t('common.generateScript')}
            </StyledButton>
            <div className="text-xs text-gray-500 dark:text-gray-400">
                {t('tools.searchQuery.disclaimer')}
            </div>
          </div>

          {message && (
            <NotificationMessage
              message={message.text}
              type={message.type}
              onDismiss={() => setMessage(null)}
            />
          )}

          {showResult && generatedScript && (
            <ScriptDisplay
              scriptContent={generatedScript}
              language="javascript"
              title={t('tools.searchQuery.generated')}
              onBack={() => {
                setShowResult(false);
                setGeneratedScript(null);
                setMessage(null);
              }}
            />
          )}
        </div>
      </ToolPageLayout>
    </TooltipProvider>
  );
};

export default SearchQueryPerformance;
