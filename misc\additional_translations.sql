-- Add missing campaign and ad performance translations
INSERT INTO content_keys (key_name, category, description) 
SELECT * FROM (VALUES
    ('tools.campaignPerformance.title', 'tools', 'Campaign Performance tool title'),
    ('tools.campaignPerformance.description', 'tools', 'Campaign Performance tool description'),
    ('tools.campaignPerformance.howItWorks.dataAggregation', 'tools', 'How it works data aggregation'),
    ('tools.campaignPerformance.howItWorks.categorization', 'tools', 'How it works categorization'),
    ('tools.campaignPerformance.howItWorks.calculations', 'tools', 'How it works calculations'),
    ('tools.campaignPerformance.howItWorks.logging', 'tools', 'How it works logging'),
    ('tools.campaignPerformance.howItWorks.googleSheetOutput', 'tools', 'How it works google sheet output'),
    ('tools.campaignPerformance.howItWorks.telegramNotifications', 'tools', 'How it works telegram notifications'),
    ('tools.campaignPerformance.howItWorks.tip', 'tools', 'Campaign performance tip'),
    ('tools.adPerformance.title', 'tools', 'Ad Performance tool title'),
    ('tools.adPerformance.description', 'tools', 'Ad Performance tool description'),
    ('tools.adPerformance.howItWorks.intro', 'tools', 'Ad Performance how it works intro'),
    ('tools.adPerformance.howItWorks.selectCampaigns', 'tools', 'Select campaigns step'),
    ('tools.adPerformance.howItWorks.fetchAds', 'tools', 'Fetch ads step'),
    ('tools.adPerformance.howItWorks.analyzeAds', 'tools', 'Analyze ads step'),
    ('tools.adPerformance.howItWorks.compareAds', 'tools', 'Compare ads step'),
    ('tools.adPerformance.howItWorks.statisticalSignificance', 'tools', 'Statistical significance step'),
    ('tools.adPerformance.howItWorks.pauseUnderperformers', 'tools', 'Pause underperformers step'),
    ('tools.adPerformance.howItWorks.applyLabel', 'tools', 'Apply label step'),
    ('tools.adPerformance.howItWorks.telegramNotifications', 'tools', 'Telegram notifications step'),
    ('tools.adPerformance.howItWorks.keyMetrics.title', 'tools', 'Key metrics title'),
    ('tools.adPerformance.howItWorks.keyMetrics.primary', 'tools', 'Primary metrics'),
    ('tools.adPerformance.howItWorks.keyMetrics.secondary', 'tools', 'Secondary metrics'),
    ('tools.adPerformance.howItWorks.important', 'tools', 'Important note')
) AS v(key_name, category, description)
WHERE NOT EXISTS (
    SELECT 1 FROM content_keys ck WHERE ck.key_name = v.key_name
);

-- Add English translations
DO $$
DECLARE
    r RECORD;
    key_id UUID;
BEGIN
    FOR r IN 
        SELECT key_name FROM content_keys 
        WHERE key_name LIKE 'tools.campaignPerformance%' 
           OR key_name LIKE 'tools.adPerformance%'
    LOOP
        SELECT id INTO key_id FROM content_keys WHERE key_name = r.key_name;
        
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        SELECT key_id, 'en', 
        CASE r.key_name
            WHEN 'tools.campaignPerformance.title' THEN 'Campaign Performance Analyzer'
            WHEN 'tools.campaignPerformance.description' THEN 'Generate Google Ads script to analyze campaign performance by traffic channel types with detailed metrics and reporting.'
            WHEN 'tools.campaignPerformance.howItWorks.dataAggregation' THEN 'Data Aggregation: The script collects campaign data from Google Ads and categorizes them by channel types (Search, Shopping, Display, etc.)'
            WHEN 'tools.campaignPerformance.howItWorks.categorization' THEN 'Categorization: Campaigns are automatically sorted into categories based on their names and types'
            WHEN 'tools.campaignPerformance.howItWorks.calculations' THEN 'Calculations: Performance metrics are calculated including CTR, CPC, conversion rates, and ROAS'
            WHEN 'tools.campaignPerformance.howItWorks.logging' THEN 'Logging: All analysis results are logged to Google Ads logs for tracking and debugging'
            WHEN 'tools.campaignPerformance.howItWorks.googleSheetOutput' THEN 'Google Sheet Output: Results are exported to a Google Sheet for easy viewing and further analysis'
            WHEN 'tools.campaignPerformance.howItWorks.telegramNotifications' THEN 'Telegram Notifications: Optional notifications sent via Telegram with summary of performance analysis'
            WHEN 'tools.campaignPerformance.howItWorks.tip' THEN 'Tip: This script is ideal for regular performance reviews and can be scheduled to run weekly or monthly'
            WHEN 'tools.adPerformance.title' THEN 'Ad Performance Analyzer'
            WHEN 'tools.adPerformance.description' THEN 'Automatically analyze and pause low-performing ads based on statistical significance CTR and conversion coefficient.'
            WHEN 'tools.adPerformance.howItWorks.intro' THEN 'This script performs automated ad performance analysis:'
            WHEN 'tools.adPerformance.howItWorks.selectCampaigns' THEN 'Select Campaigns: The script filters campaigns based on your specified criteria'
            WHEN 'tools.adPerformance.howItWorks.fetchAds' THEN 'Fetch Ads: Retrieves all ads from selected campaigns with their performance data'
            WHEN 'tools.adPerformance.howItWorks.analyzeAds' THEN 'Analyze Ads: Calculates performance metrics for each ad including CTR and conversion rates'
            WHEN 'tools.adPerformance.howItWorks.compareAds' THEN 'Compare Ads: Compares each ad performance against the campaign average'
            WHEN 'tools.adPerformance.howItWorks.statisticalSignificance' THEN 'Statistical Significance: Applies statistical tests to ensure decisions are data-driven'
            WHEN 'tools.adPerformance.howItWorks.pauseUnderperformers' THEN 'Pause Underperformers: Automatically pauses ads that significantly underperform'
            WHEN 'tools.adPerformance.howItWorks.applyLabel' THEN 'Apply Label: Adds labels to analyzed ads for easy identification and tracking'
            WHEN 'tools.adPerformance.howItWorks.telegramNotifications' THEN 'Telegram Notifications: Sends summary reports of actions taken and performance insights'
            WHEN 'tools.adPerformance.howItWorks.keyMetrics.title' THEN 'Key Metrics Analyzed:'
            WHEN 'tools.adPerformance.howItWorks.keyMetrics.primary' THEN 'Primary: CTR (Click-Through Rate) and Conversion Rate'
            WHEN 'tools.adPerformance.howItWorks.keyMetrics.secondary' THEN 'Secondary: Impressions, Clicks, Cost, and Quality Score'
            WHEN 'tools.adPerformance.howItWorks.important' THEN 'Important: This script requires sufficient data for statistical significance. Ensure ads have at least 100 impressions for reliable analysis.'
            ELSE 'Translation needed'
        END
        WHERE NOT EXISTS (
            SELECT 1 FROM content_translations ct 
            WHERE ct.content_key_id = key_id AND ct.language_code = 'en'
        );
        
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        SELECT key_id, 'ua',
        CASE r.key_name
            WHEN 'tools.campaignPerformance.title' THEN 'Аналізатор продуктивності кампаній'
            WHEN 'tools.campaignPerformance.description' THEN 'Генерує скрипт Google Ads для аналізу продуктивності кампаній за типами каналів реклами з детальними метриками та звітністю.'
            WHEN 'tools.campaignPerformance.howItWorks.dataAggregation' THEN 'Агрегація даних: Скрипт збирає дані кампаній з Google Ads і категоризує їх за типами каналів (Пошук, Покупки, Медійна реклама тощо)'
            WHEN 'tools.campaignPerformance.howItWorks.categorization' THEN 'Категоризація: Кампанії автоматично сортуються на категорії на основі їх назв і типів'
            WHEN 'tools.campaignPerformance.howItWorks.calculations' THEN 'Розрахунки: Обчислюються метрики продуктивності включаючи CTR, CPC, коефіцієнти конверсії та ROAS'
            WHEN 'tools.campaignPerformance.howItWorks.logging' THEN 'Логування: Всі результати аналізу записуються в логи Google Ads для відстеження та налагодження'
            WHEN 'tools.campaignPerformance.howItWorks.googleSheetOutput' THEN 'Вивід в Google Таблиці: Результати експортуються в Google Таблиці для зручного перегляду та подальшого аналізу'
            WHEN 'tools.campaignPerformance.howItWorks.telegramNotifications' THEN 'Сповіщення Telegram: Необов''язкові сповіщення надсилаються через Telegram зі зведенням аналізу продуктивності'
            WHEN 'tools.campaignPerformance.howItWorks.tip' THEN 'Підказка: Цей скрипт ідеально підходить для регулярних оглядів продуктивності і може бути запланований для запуску щотижня або щомісяця'
            WHEN 'tools.adPerformance.title' THEN 'Аналізатор продуктивності оголошень'
            WHEN 'tools.adPerformance.description' THEN 'Автоматично аналізує та призупиняє низькоефективні оголошення на основі статистичної значущості CTR та коефіцієнта конверсії.'
            WHEN 'tools.adPerformance.howItWorks.intro' THEN 'Цей скрипт виконує автоматизований аналіз продуктивності оголошень:'
            WHEN 'tools.adPerformance.howItWorks.selectCampaigns' THEN 'Вибір кампаній: Скрипт фільтрує кампанії на основі ваших зазначених критеріїв'
            WHEN 'tools.adPerformance.howItWorks.fetchAds' THEN 'Отримання оголошень: Витягує всі оголошення з вибраних кампаній з їх даними продуктивності'
            WHEN 'tools.adPerformance.howItWorks.analyzeAds' THEN 'Аналіз оголошень: Обчислює метрики продуктивності для кожного оголошення включаючи CTR і коефіцієнти конверсії'
            WHEN 'tools.adPerformance.howItWorks.compareAds' THEN 'Порівняння оголошень: Порівнює продуктивність кожного оголошення з середнім по кампанії'
            WHEN 'tools.adPerformance.howItWorks.statisticalSignificance' THEN 'Статистична значущість: Застосовує статистичні тести для забезпечення рішень на основі даних'
            WHEN 'tools.adPerformance.howItWorks.pauseUnderperformers' THEN 'Призупинення неефективних: Автоматично призупиняє оголошення, які значно недопрацьовують'
            WHEN 'tools.adPerformance.howItWorks.applyLabel' THEN 'Застосування міток: Додає мітки до проаналізованих оголошень для легкої ідентифікації та відстеження'
            WHEN 'tools.adPerformance.howItWorks.telegramNotifications' THEN 'Сповіщення Telegram: Надсилає зведені звіти про вжиті дії та інсайти продуктивності'
            WHEN 'tools.adPerformance.howItWorks.keyMetrics.title' THEN 'Ключові аналізовані метрики:'
            WHEN 'tools.adPerformance.howItWorks.keyMetrics.primary' THEN 'Основні: CTR (показник кліків) та коефіцієнт конверсії'
            WHEN 'tools.adPerformance.howItWorks.keyMetrics.secondary' THEN 'Другорядні: Покази, кліки, вартість та показник якості'
            WHEN 'tools.adPerformance.howItWorks.important' THEN 'Важливо: Цей скрипт потребує достатньо даних для статистичної значущості. Переконайтеся, що оголошення мають принаймні 100 показів для надійного аналізу.'
            ELSE 'Потребує перекладу'
        END
        WHERE NOT EXISTS (
            SELECT 1 FROM content_translations ct 
            WHERE ct.content_key_id = key_id AND ct.language_code = 'ua'
        );
    END LOOP;
END $$;