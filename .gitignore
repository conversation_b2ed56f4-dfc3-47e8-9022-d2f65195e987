# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*~
desktop.ini

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build artifacts and cache
dist/
build/
.cache/
.vite/
.next/
.nuxt/
.output/
.vercel/
.netlify/
.turbo/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# Logs
logs/
*.log
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/
.c8_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Firebase cache and config
.firebase/
firebase-debug.log*
firebase-debug.*.log*
.firebaserc

# Large files and archives
*.zip
*.tar.gz
*.rar
*.7z

# Temporary files
tmp/
temp/
.tmp/

# OS generated files
.directory
.fuse_hidden*

# TypeScript cache
*.tsbuildinfo

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Package manager lock files (keep package-lock.json but ignore others if needed)
# yarn.lock
# pnpm-lock.yaml

# Local development certificates
*.pem
*.key
*.crt

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Test files
test-results/
playwright-report/
playwright/.cache/

# Storybook build outputs
storybook-static/

# Sentry
.sentryclirc
