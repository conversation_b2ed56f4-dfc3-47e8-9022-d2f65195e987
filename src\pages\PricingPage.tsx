import React from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import { useLanguage } from '../contexts/LanguageContext';

const PricingPage: React.FC = () => {
  const { t } = useLanguage();
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow bg-gradient-to-b from-gray-900 to-gray-800 text-white overflow-hidden">
        <div className="relative pt-28 pb-20 md:pt-36 md:pb-28">
          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-blue-900/20 to-transparent"></div>
            <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
          </div>
          
          {/* Main content */}
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">{t('pricing.page.title')}</span>
              </h1>

              <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-gray-700/50 max-w-3xl mx-auto">
                <div className="mb-8">
                  <div className="inline-flex items-center px-4 py-1.5 rounded-full text-sm font-medium bg-blue-500/10 text-blue-400 mb-4">
                    <span className="relative flex h-2 w-2 mr-2">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-400"></span>
                    </span>
                    {t('pricing.beta.badge')}
                  </div>

                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">{t('pricing.beta.title')}</h2>
                  <p className="text-lg text-gray-300 mb-8">
                    {t('pricing.beta.description')}
                  </p>

                  <p className="text-lg text-gray-300 mb-8">
                    {t('pricing.beta.contact_info')}
                  </p>
                  
                  <div className="flex flex-col sm:flex-row justify-center gap-4 mt-10">
                    <Link
                      to="/contact"
                      className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
                    >
                      {t('pricing.buttons.contact')}
                    </Link>
                    <Link
                      to="/services"
                      className="inline-flex items-center justify-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md text-white bg-transparent hover:bg-gray-700/50 transition-colors duration-200"
                    >
                      {t('pricing.buttons.services')}
                    </Link>
                  </div>
                </div>
                
                <div className="pt-8 mt-8 border-t border-gray-700/50">
                  <h3 className="text-lg font-medium text-white mb-4">{t('pricing.beta.features.title')}</h3>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                    <li className="flex items-start">
                      <svg className="h-5 w-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-gray-300">{t('pricing.beta.features.early_access')}</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="h-5 w-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-gray-300">{t('pricing.beta.features.direct_support')}</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="h-5 w-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-gray-300">{t('pricing.beta.features.influence_development')}</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="h-5 w-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-gray-300">{t('pricing.beta.features.special_pricing')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default PricingPage;
