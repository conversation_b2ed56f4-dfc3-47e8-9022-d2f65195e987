import React, { useState, useEffect } from 'react';
import { TooltipProvider } from "@/components/ui/tooltip";
import { Bell, FileText, Settings, Filter as FilterIcon } from 'lucide-react';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledSelect from '../ui/shared/StyledSelect';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

// Ambient declarations for Google Ads Script global objects
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var AdsApp: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var Logger: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var Utilities: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var MailApp: any;
// eslint-disable-next-line no-var, @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare var UrlFetchApp: any;

const LOCAL_STORAGE_PREFIX = 'searchQueryAnalyzer_';

const GOOGLE_ADS_DATE_RANGES_SQ = [
  'TODAY',
  'YESTERDAY',
  'LAST_7_DAYS',
  'LAST_14_DAYS',
  'LAST_30_DAYS',
  'THIS_MONTH',
  'LAST_MONTH',
  'LAST_90_DAYS'
];

const NEGATIVE_MATCH_TYPE_OPTIONS = ['BROAD', 'PHRASE', 'EXACT'];

const SearchQueryAnalyzer: React.FC = () => {
  // Form inputs
  const [campaignName, setCampaignName] = useState('');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');
  const [emailAddress, setEmailAddress] = useState('');
  const [minImpressions, setMinImpressions] = useState('50');
  const [maxQueries, setMaxQueries] = useState('100');
  const [negativeMatchTypes, setNegativeMatchTypes] = useState<string[]>(['EXACT', 'PHRASE']);

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [showTelegramSettings, setShowTelegramSettings] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  useEffect(() => {
    const fields = [
      { key: 'campaignName', setter: setCampaignName, type: 'string' },
      { key: 'dateRange', setter: setDateRange, type: 'string' },
      { key: 'emailAddress', setter: setEmailAddress, type: 'string' },
      { key: 'minImpressions', setter: setMinImpressions, type: 'string' },
      { key: 'maxQueries', setter: setMaxQueries, type: 'string' },
      { key: 'negativeMatchTypes', setter: setNegativeMatchTypes, type: 'string[]' },
      { key: 'useTelegram', setter: setUseTelegram, type: 'boolean' },
      { key: 'telegramBotToken', setter: setTelegramBotToken, type: 'string' },
      { key: 'telegramChatId', setter: setTelegramChatId, type: 'string' },
    ];

    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field.key}`);
      if (savedValue !== null) {
        if (field.type === 'boolean') {
          const booleanValue = savedValue === 'true';
          (field.setter as React.Dispatch<React.SetStateAction<boolean>>)(booleanValue);
          if (field.key === 'useTelegram') {
            setShowTelegramSettings(booleanValue);
          }
        } else if (field.type === 'string[]') {
          try {
            const arrayValue = JSON.parse(savedValue);
            if (Array.isArray(arrayValue)) {
              (field.setter as React.Dispatch<React.SetStateAction<string[]>>)(arrayValue);
            }
          } catch (e) {
            console.error(`Failed to parse ${field.key} from localStorage:`, e);
          }
        } else {
          (field.setter as React.Dispatch<React.SetStateAction<string>>)(savedValue);
        }
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setMessage(null);
    const target = e.target as HTMLInputElement;
    const { name, value, type } = target;
    
    if (name === 'negativeMatchTypes') {
      const newMatchTypes = [...negativeMatchTypes];
      const matchTypeIndex = newMatchTypes.indexOf(value);
      
      if (matchTypeIndex > -1) {
        newMatchTypes.splice(matchTypeIndex, 1);
      } else {
        newMatchTypes.push(value);
      }
      
      setNegativeMatchTypes(newMatchTypes);
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, JSON.stringify(newMatchTypes));
    } else {
      // Handle different field types separately to ensure type safety
      if (name === 'useTelegram') {
        // Handle boolean checkbox separately
        const isChecked = target.checked;
        setUseTelegram(isChecked);
        localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(isChecked));
        setShowTelegramSettings(isChecked);
        if (!isChecked) {
          setTelegramBotToken('');
          setTelegramChatId('');
          localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramBotToken`);
          localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramChatId`);
        }
      } else {
        // Handle string inputs
        const stringValue = value as string;
        switch (name) {
          case 'campaignName':
            setCampaignName(stringValue);
            break;
          case 'dateRange':
            setDateRange(stringValue);
            break;
          case 'emailAddress':
            setEmailAddress(stringValue);
            break;
          case 'minImpressions':
            setMinImpressions(stringValue);
            break;
          case 'maxQueries':
            setMaxQueries(stringValue);
            break;
          case 'telegramBotToken':
            setTelegramBotToken(stringValue);
            break;
          case 'telegramChatId':
            setTelegramChatId(stringValue);
            break;
        }
        localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, stringValue);
      }
    }
    }
  };

  const handleGenerateScript = () => {
    setMessage(null);

    if (!campaignName.trim()) {
      setMessage({ text: 'Campaign Name is required.', type: 'error' });
      return;
    }
    if (!emailAddress.trim() || !/\S+@\S+\.\S+/.test(emailAddress)) {
      setMessage({ text: 'Please enter a valid email address for notifications.', type: 'error' });
      return;
    }
    if (useTelegram && (!telegramBotToken.trim() || !telegramChatId.trim())) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for Telegram notifications.', type: 'error' });
      return;
    }
    if (negativeMatchTypes.length === 0) {
      setMessage({ text: 'Please select at least one negative match type.', type: 'error' });
      return;
    }

    const script = generateSearchQueryScript(
      campaignName,
      dateRange,
      emailAddress,
      minImpressions,
      maxQueries,
      negativeMatchTypes,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('resultSectionSQA')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const generateSearchQueryScript = (
    campaignNameVal: string,
    dateRangeVal: string,
    emailVal: string,
    minImpressionsVal: string,
    maxQueriesVal: string,
    negativeMatchTypesVal: string[],
    useTelegramVal: boolean,
    botTokenVal: string,
    chatIdVal: string
  ): string => {
    const uniquePrefix: string = 'sqa_' + Date.now().toString(36) + '_';
    let telegramCode = '';
    if (useTelegramVal) {
      telegramCode = `
  // eslint-disable-next-line
  function ${uniquePrefix}sendTelegramNotification(message) {
    var telegramBotToken = '${botTokenVal.replace(new RegExp('\\\\', 'g'), '\\\\\\\\').replace(new RegExp("'", 'g'), "\\'")}';
    var telegramChatId = '${chatIdVal.replace(new RegExp('\\\\', 'g'), '\\\\\\\\').replace(new RegExp("'", 'g'), "\\'")}';
    if (!telegramBotToken || !telegramChatId) {
      Logger.log('Telegram Bot Token or Chat ID is missing. Skipping Telegram notification.');
      return;
    }
    var payload = {
      'chat_id': telegramChatId,
      'text': message,
      'parse_mode': 'HTML'
    };
    var options = {
      'method': 'post',
      'contentType': 'application/json',
      'payload': JSON.stringify(payload)
    };
    try {
      UrlFetchApp.fetch('https://api.telegram.org/bot' + telegramBotToken + '/sendMessage', options);
      Logger.log('Telegram notification sent.');
    } catch (e) {
      Logger.log('Telegram Error: ' + e.toString() + ' Payload: ' + JSON.stringify(payload));
    }
  }
`;
    }

    /* eslint-disable */
    // Typing as 'any' to prevent TS from deep parsing the script string content.
    // ESLint is disabled for this block anyway.
    const mainScript: any = `
function main() {
  var CONFIG = {
    CAMPAIGN_NAME_CONTAINS: "${campaignNameVal.replace(/"/g, '\\"')}",
    EMAIL_ADDRESS: "${emailVal.replace(/"/g, '\\"')}",
    DATE_RANGE: "${dateRangeVal}",
    MIN_IMPRESSIONS: parseInt("${minImpressionsVal}", 10) || 50,
    MAX_QUERIES_TO_REPORT: parseInt("${maxQueriesVal}", 10) || 100,
    NEGATIVE_MATCH_TYPES: [${negativeMatchTypesVal.map(type => `"${type}"`).join(', ')}],
    INCLUDE_QUERIES_WITH_ZERO_CONVERSIONS: true, // Set to false to only include queries with zero conversions
    USE_TELEGRAM: ${useTelegramVal}
  };

  var ${uniquePrefix}accountTimezone = AdsApp.currentAccount().getTimeZone();
  var ${uniquePrefix}formattedTime = Utilities.formatDate(new Date(), ${uniquePrefix}accountTimezone, 'yyyy-MM-dd HH:mm:ss');
  var ${uniquePrefix}emailSubject = 'Search Query Analysis Report - ' + CONFIG.CAMPAIGN_NAME_CONTAINS + ' - ' + ${uniquePrefix}formattedTime;
  var ${uniquePrefix}emailBody = 'Search Query Analysis Report Generated: ' + ${uniquePrefix}formattedTime + '\n\n';
  ${uniquePrefix}emailBody += 'Campaigns containing: "' + CONFIG.CAMPAIGN_NAME_CONTAINS + '"\n';
  ${uniquePrefix}emailBody += 'Date Range: ' + CONFIG.DATE_RANGE + '\n';
  ${uniquePrefix}emailBody += 'Minimum Impressions per Query: ' + CONFIG.MIN_IMPRESSIONS + '\n';
  ${uniquePrefix}emailBody += 'Max Queries to Report: ' + CONFIG.MAX_QUERIES_TO_REPORT + '\n';
  ${uniquePrefix}emailBody += 'Selected Negative Match Types: ' + CONFIG.NEGATIVE_MATCH_TYPES.join(', ') + '\n\n';

  Logger.log('Starting Search Query Analysis Script...');
  Logger.log('Configuration: ' + JSON.stringify(CONFIG));

  var ${uniquePrefix}campaignSelector = AdsApp.campaigns()
    .withCondition("campaign.name CONTAINS_IGNORE_CASE '" + CONFIG.CAMPAIGN_NAME_CONTAINS + "'")
    .withCondition('campaign.status = ENABLED');

  var ${uniquePrefix}campaignIterator = ${uniquePrefix}campaignSelector.get();
  var ${uniquePrefix}allReportData = [];
  var ${uniquePrefix}processedCampaigns = 0;

  if (!${uniquePrefix}campaignIterator.hasNext()) {
    ${uniquePrefix}emailBody += 'No active campaigns found matching the pattern: "' + CONFIG.CAMPAIGN_NAME_CONTAINS + '".\n';
    Logger.log('No active campaigns found.');
  } else {
    ${uniquePrefix}emailBody += '--- Search Query Performance Data ---\n';
  }

  while (${uniquePrefix}campaignIterator.hasNext()) {
    var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
    var ${uniquePrefix}campaignName = ${uniquePrefix}campaign.getName();
    ${uniquePrefix}processedCampaigns++;
    Logger.log('Processing Campaign: ' + ${uniquePrefix}campaignName + ' (ID: ' + ${uniquePrefix}campaign.getId() + ')');
    ${uniquePrefix}emailBody += '\n<b>Campaign: ' + ${uniquePrefix}campaignName + '</b>\n';

    var ${uniquePrefix}reportQuery = "SELECT query, metrics.impressions, metrics.clicks, metrics.cost_micros, metrics.conversions, metrics.conversions_value, campaign.name " +
                       "FROM search_term_view " +
                       "WHERE campaign.id = " + ${uniquePrefix}campaign.getId() + " " +
                       "AND segments.date DURING " + CONFIG.DATE_RANGE + " " +
                       "AND metrics.impressions >= " + CONFIG.MIN_IMPRESSIONS + " " +
                       (CONFIG.INCLUDE_QUERIES_WITH_ZERO_CONVERSIONS ? "" : "AND metrics.conversions = 0 ") +
                       "ORDER BY metrics.cost_micros DESC " +
                       "LIMIT " + CONFIG.MAX_QUERIES_TO_REPORT;

    var ${uniquePrefix}report = AdsApp.search(${uniquePrefix}reportQuery);
    var ${uniquePrefix}queriesFoundInCampaign = false;

    var ${uniquePrefix}campaignTable = '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 12px;">' +
                           '<thead><tr style="background-color: #f2f2f2;">' +
                           '<th>Search Query</th><th>Neg. Exact</th><th>Neg. Phrase</th><th>Impr.</th><th>Clicks</th><th>CTR</th><th>Cost</th><th>Conv.</th><th>Cost/Conv.</th><th>Conv. Value</th><th>ROAS</th></tr></thead><tbody>';

    while (${uniquePrefix}report.hasNext()) {
      var ${uniquePrefix}row = ${uniquePrefix}report.next();
      ${uniquePrefix}queriesFoundInCampaign = true;

      var query = ${uniquePrefix}row.query;
      var impressions = ${uniquePrefix}row.metrics.impressions;
      var clicks = ${uniquePrefix}row.metrics.clicks;
      var cost = ${uniquePrefix}row.metrics.costMicros / 1000000;
      var conversions = ${uniquePrefix}row.metrics.conversions;
      var conversionsValue = ${uniquePrefix}row.metrics.conversionsValue;

      var ctr = clicks / impressions;
      var costPerConversion = conversions > 0 ? cost / conversions : 0;
      var roas = conversionsValue > 0 ? conversionsValue / cost : 0;

      var negativeExact = '"' + query.replace(/"/g, '""') + '"';
      var negativePhrase = '"[' + query.replace(/"/g, '""') + ']"';

      ${uniquePrefix}campaignTable += '<tr>' +
                             '<td>' + query + '</td>' +
                             '<td>' + negativeExact + '</td>' +
                             '<td>' + negativePhrase + '</td>' +
                             '<td>' + impressions + '</td>' +
                             '<td>' + clicks + '</td>' +
                             '<td>' + (ctr * 100).toFixed(2) + '%</td>' +
                             '<td>' + cost.toFixed(2) + '</td>' +
                             '<td>' + conversions.toFixed(2) + '</td>' +
                             '<td>' + costPerConversion.toFixed(2) + '</td>' +
                             '<td>' + conversionsValue.toFixed(2) + '</td>' +
                             '<td>' + roas.toFixed(2) + '</td></tr>';

      ${uniquePrefix}allReportData.push({
        campaign: ${uniquePrefix}campaignName,
        query: query,
        impressions: impressions,
        clicks: clicks,
        cost: cost,
        conversions: conversions,
        conversionsValue: conversionsValue
      });
    }
    
    if (!${uniquePrefix}queriesFoundInCampaign) {
      ${uniquePrefix}campaignTable += '<tr><td colspan="11">No queries meeting the criteria found in this campaign.</td></tr>';
      ${uniquePrefix}emailBody += 'No queries meeting the criteria found in this campaign.\n';
    } else {
      ${uniquePrefix}emailBody += ${uniquePrefix}campaignTable + '</tbody></table>\n';
    }
  }

  if (${uniquePrefix}processedCampaigns === 0) {
      ${uniquePrefix}emailBody += 'No campaigns processed or found matching criteria.\n';
  } else {
      ${uniquePrefix}emailBody += '\n--- Summary ---\n';
      ${uniquePrefix}emailBody += 'Processed ' + ${uniquePrefix}processedCampaigns + ' campaign(s).\n';
      if (${uniquePrefix}allReportData.length === 0) {
        ${uniquePrefix}emailBody += 'No search queries met the reporting criteria across all processed campaigns.\n';
      }
  }

  var ${uniquePrefix}emailBodyForTelegram = ${uniquePrefix}emailBody.replace(/<[^>]+>/g, ''); // Basic HTML removal for Telegram
  ${uniquePrefix}emailBodyForTelegram = ${uniquePrefix}emailBodyForTelegram.substring(0, 4000); // Telegram message limit


  Logger.log(${uniquePrefix}emailBody);

  if (CONFIG.EMAIL_ADDRESS) {
    MailApp.sendEmail({
      to: CONFIG.EMAIL_ADDRESS,
      subject: ${uniquePrefix}emailSubject,
      htmlBody: ${uniquePrefix}emailBody
    });
    Logger.log('Email report sent to: ' + CONFIG.EMAIL_ADDRESS);
  }

  if (CONFIG.USE_TELEGRAM) {
    ${uniquePrefix}sendTelegramNotification(${uniquePrefix}emailSubject + '\n\n' + ${uniquePrefix}emailBodyForTelegram);
  }

  Logger.log('Script finished.');
}
`;
    /* eslint-enable */

    return telegramCode + mainScript;
  };

  return (
    <TooltipProvider>
      <ToolPageLayout
        title="Search Query Analyzer & Negative Keyword Tool"
        description="Analyze search query performance to identify potential negative keywords (high cost, low conversion) and positive keywords (low cost, high impression/conversion). Generates a Google Ads script for bulk negative keyword addition based on your criteria."
      >
        {message && (
          <NotificationMessage
            type={message.type}
            message={message.text}
            onDismiss={() => setMessage(null)}
            className="mb-6"
          />
        )}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 items-start">
          <div className="md:col-span-2 space-y-6 sm:space-y-8">
            <FormSection title="Campaign & Report Settings" icon={<FileText />} theme="sky">
              <FormItem 
                label="Campaign Name Contains"
                htmlFor="campaignName"
                tooltipText="Enter a part of the campaign name (e.g., 'Brand' or 'Shopping_Electronics'). The script will process campaigns whose names include this text. Case-sensitive."
                required
              >
                <StyledInput
                  id="campaignName"
                  name="campaignName"
                  value={campaignName}
                  onChange={handleInputChange}
                  placeholder="e.g., Brand_Search_Exact"
                />
              </FormItem>
              <FormItem 
                label="Date Range"
                htmlFor="dateRange"
                tooltipText="Select the period for which the search query data should be analyzed."
                required
              >
                <StyledSelect
                  id="dateRange"
                  name="dateRange"
                  value={dateRange}
                  onChange={handleInputChange}
                >
                  {GOOGLE_ADS_DATE_RANGES_SQ.map(range => (
                    <option key={range} value={range}>{range.replace(/_/g, ' ')}</option>
                  ))}
                </StyledSelect>
              </FormItem>
              <FormItem 
                label="Email Address for Notifications"
                htmlFor="emailAddress"
                tooltipText="The script will send a summary email to this address upon completion."
                required
              >
                <StyledInput
                  type="email"
                  id="emailAddress"
                  name="emailAddress"
                  value={emailAddress}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                />
              </FormItem>
            </FormSection>

            <FormSection title="Filtering & Thresholds" icon={<FilterIcon />} theme="emerald">
              <FormItem 
                label="Minimum Impressions"
                htmlFor="minImpressions"
                tooltipText="Queries with impressions below this number will be ignored."
                required
              >
                <StyledInput
                  type="number"
                  id="minImpressions"
                  name="minImpressions"
                  value={minImpressions}
                  onChange={handleInputChange}
                  placeholder="e.g., 50"
                />
              </FormItem>
              <FormItem 
                label="Maximum Queries to Process"
                htmlFor="maxQueries"
                tooltipText="Limit the number of queries the script will analyze to prevent timeouts. Queries with the highest impressions are prioritized."
                required
              >
                <StyledInput
                  type="number"
                  id="maxQueries"
                  name="maxQueries"
                  value={maxQueries}
                  onChange={handleInputChange}
                  placeholder="e.g., 100 or 500"
                />
              </FormItem>
              <FormItem 
                label="Negative Keyword Match Types"
                tooltipText="Select the match types for the negative keywords that will be created by the script."
                required
              >
                <div className="space-y-2 mt-1">
                  {NEGATIVE_MATCH_TYPE_OPTIONS.map((matchType) => (
                    <label key={matchType} className="flex items-center space-x-2 text-sm text-gray-300 cursor-pointer">
                      <StyledInput
                        type="checkbox"
                        name="negativeMatchTypes"
                        value={matchType}
                        checked={negativeMatchTypes.includes(matchType)}
                        onChange={handleInputChange}
                        className="form-checkbox h-4 w-4 text-sky-500 bg-slate-700 border-slate-600 focus:ring-sky-500 rounded transition duration-150 ease-in-out"
                      />
                      <span>{matchType.charAt(0).toUpperCase() + matchType.slice(1).toLowerCase()}</span>
                    </label>
                  ))}
                </div>
              </FormItem>
            </FormSection>

            <FormSection title="Telegram Notifications (Optional)" icon={<Bell />} theme="purple">
              <FormItem 
                label="Enable Telegram Notifications"
                htmlFor="useTelegram"
                tooltipText="Check this box if you want to receive notifications via Telegram in addition to email."
              >
                <StyledInput
                  type="checkbox"
                  id="useTelegram"
                  name="useTelegram"
                  checked={useTelegram}
                  onChange={handleInputChange}
                  className="form-checkbox h-4 w-4 text-purple-500 bg-slate-700 border-slate-600 focus:ring-purple-500 rounded transition duration-150 ease-in-out"
                />
              </FormItem>
              {showTelegramSettings && (
                <>
                  <FormItem 
                    label="Telegram Bot Token"
                    htmlFor="telegramBotToken"
                    tooltipText="Enter your Telegram Bot Token. Ensure it's kept secure."
                    required={useTelegram} // Required only if useTelegram is true
                  >
                    <StyledInput
                      type="password"
                      id="telegramBotToken"
                      name="telegramBotToken"
                      value={telegramBotToken}
                      onChange={handleInputChange}
                      placeholder="Your Telegram Bot Token"
                    />
                  </FormItem>
                  <FormItem 
                    label="Telegram Chat ID"
                    htmlFor="telegramChatId"
                    tooltipText="Enter the Chat ID where notifications should be sent (your personal ID or a group/channel ID)."
                    required={useTelegram} // Required only if useTelegram is true
                  >
                    <StyledInput
                      id="telegramChatId"
                      name="telegramChatId"
                      value={telegramChatId}
                      onChange={handleInputChange}
                      placeholder="Your Telegram Chat ID"
                    />
                  </FormItem>
                </>
              )}
            </FormSection>

            <div className="flex justify-end mt-6 sm:mt-8">
              <StyledButton 
                onClick={handleGenerateScript} 
                themeColor="sky"
                leftIcon={<Settings className="h-4 w-4 mr-2" />} 
                className="px-6 py-2.5 text-base"
                aria-label="Generate Script and Notify"
              >
                Generate Script & Notify
              </StyledButton>
            </div>
          </div>
          
          <div className="md:col-span-1 space-y-6 sm:space-y-8 sticky top-8">
            {showResult && generatedScript && (
              <div id="resultSectionSQA">
                <ScriptDisplay
                  scriptContent={generatedScript} 
                  title="Generated Search Query Analysis Script"
                  language="javascript"
                  onBack={() => {
                    setShowResult(false);
                    setGeneratedScript('');
                    setMessage(null);
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </ToolPageLayout>
    </TooltipProvider>
  );
};

export default SearchQueryAnalyzer;
