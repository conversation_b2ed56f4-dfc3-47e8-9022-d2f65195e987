import React from 'react';
import { Link } from 'react-router-dom';
import Button from './ui/Button';
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>2, Zap, Clock, Shield } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon: Icon, title, description }) => (
  <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-blue-400/30 transition-all duration-300 hover:shadow-lg">
    <div className="w-12 h-12 rounded-lg bg-blue-600/20 flex items-center justify-center mb-4">
      <Icon className="w-6 h-6 text-blue-400" />
    </div>
    <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
    <p className="text-blue-100/80">{description}</p>
  </div>
);

const Hero: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="bg-gradient-to-b from-gray-900 to-gray-800 text-white overflow-hidden">
      {/* Hero Section */}
      <section className="relative pt-28 pb-20 md:pt-36 md:pb-28">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-blue-900/20 to-transparent"></div>
          <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
        </div>
        
        <div className="relative w-4/5 mx-auto">
          <div className="max-w-7xl mx-auto">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 text-blue-300 text-sm font-medium mb-6">
                <span className="flex h-2 w-2 mr-2 rounded-full bg-blue-400 animate-pulse"></span>
                {t('hero.badge')}
              </div>
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-200 mb-6">
                {t('hero.title.line1')}
                <span className="block mt-2 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  {t('hero.title.line2')}
                </span>
              </h1>
              <p className="text-lg md:text-xl text-blue-100/90 max-w-3xl mx-auto mb-16">
                {t('hero.subtitle')}
              </p>
              
              <div className="relative max-w-5xl mx-auto">
                <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-2xl blur-2xl opacity-70 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"></div>
                <div className="relative bg-gradient-to-br from-gray-900 to-gray-800 border border-white/10 rounded-xl overflow-hidden shadow-2xl">
                  <div className="p-1 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20">
                    <div className="bg-gray-900/80 rounded-lg overflow-hidden">
                      <div className="p-4 border-b border-white/5 flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <div className="flex-1 text-center text-xs font-mono text-gray-400">dashboard.js</div>
                      </div>
                      <div className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div className="bg-gray-800/50 p-4 rounded-lg border border-white/5">
                            <div className="text-sm text-blue-400 mb-2">Total Spend</div>
                            <div className="text-2xl font-bold">$12,845</div>
                            <div className="text-xs text-green-400 mt-1">+12.5% from last month</div>
                          </div>
                          <div className="bg-gray-800/50 p-4 rounded-lg border border-white/5">
                            <div className="text-sm text-blue-400 mb-2">Conversions</div>
                            <div className="text-2xl font-bold">1,248</div>
                            <div className="text-xs text-green-400 mt-1">+8.3% from last month</div>
                          </div>
                          <div className="bg-gray-800/50 p-4 rounded-lg border border-white/5">
                            <div className="text-sm text-blue-400 mb-2">Avg. CPA</div>
                            <div className="text-2xl font-bold">$24.56</div>
                            <div className="text-xs text-red-400 mt-1">-5.2% from last month</div>
                          </div>
                        </div>
                        <div className="mt-8 pt-6 border-t border-white/5">
                          <div className="text-center">
                            <Link to="/contact" className="group inline-block">
                              <Button 
                                size="lg"
                                variant="secondary"
                                className="font-semibold group-hover:shadow-lg group-hover:scale-105 transition-all duration-300 w-full sm:w-auto px-8 py-3"
                              >
                                {t('hero.cta.button')}
                                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="pt-0 pb-16 md:py-24 bg-gradient-to-b from-gray-800/50 to-gray-900/50">
        <div className="w-4/5 mx-auto">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">{t('hero.features.title')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">{t('hero.features.highlight')}</span></h2>
            <p className="text-lg text-blue-100/80">{t('hero.features.subtitle')}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <FeatureCard
              icon={BarChart2}
              title={t('hero.features.analytics.title')}
              description={t('hero.features.analytics.description')}
            />
            <FeatureCard
              icon={Zap}
              title={t('hero.features.ai.title')}
              description={t('hero.features.ai.description')}
            />
            <FeatureCard
              icon={Clock}
              title={t('hero.features.automation.title')}
              description={t('hero.features.automation.description')}
            />
            <FeatureCard
              icon={Shield}
              title={t('hero.features.security.title')}
              description={t('hero.features.security.description')}
            />
          </div>
          
          <div className="text-center">
            <Link to="/contact" className="inline-flex items-center text-blue-400 hover:text-blue-300 group font-medium">
              {t('hero.contact.link')}
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Hero;