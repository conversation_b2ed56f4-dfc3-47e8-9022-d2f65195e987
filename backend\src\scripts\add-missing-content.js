const { query: dbQuery } = require('../database/connection');

const missingContent = [
  // SEO keys
  { key: 'seo.meta_title_en', category: 'seo', en: 'gAds Supercharge - Google Ads Automation & Script Generator Platform', ua: 'gAds Supercharge - Платформа Автоматизації Google Ads і Генератор Скриптів' },
  { key: 'seo.meta_description_en', category: 'seo', en: 'Professional Google Ads automation platform with 12+ script generators, budget optimization, performance analysis, and bilingual support. Boost your campaigns efficiency.', ua: 'Професійна платформа автоматизації Google Ads з 12+ генераторами скриптів, оптимізацією бюджету, аналізом ефективності та двомовною підтримкою.' },
  { key: 'seo.keywords_en', category: 'seo', en: 'google ads, automation, script generator, budget optimization, campaign management, performance analysis, ppc tools', ua: 'google ads, автоматизація, генератор скриптів, оптимізація бюджету, управління кампаніями, аналіз ефективності, ppc інструменти' },
  
  // About page
  { key: 'about.title', category: 'about', en: 'About gAds Supercharge', ua: 'Про gAds Supercharge' },
  { key: 'about.subtitle', category: 'about', en: 'Leading Google Ads Automation Platform', ua: 'Провідна Платформа Автоматизації Google Ads' },
  { key: 'about.description', category: 'about', en: 'We provide cutting-edge automation tools for Google Ads professionals, helping agencies and businesses optimize their campaigns with intelligent script generation and performance analysis.', ua: 'Ми надаємо передові інструменти автоматизації для професіоналів Google Ads, допомагаючи агенціям та бізнесу оптимізувати кампанії за допомогою інтелектуального генерування скриптів та аналізу ефективності.' },

  // Careers page
  { key: 'careers.title', category: 'careers', en: 'Join Our Team', ua: 'Приєднуйтесь до Нашої Команди' },
  { key: 'careers.subtitle', category: 'careers', en: 'Build the Future of Google Ads Automation', ua: 'Будуйте Майбутнє Автоматизації Google Ads' },
  { key: 'careers.header.title', category: 'careers', en: 'Careers at gAds Supercharge', ua: 'Кар\'єра в gAds Supercharge' },
  { key: 'careers.header.subtitle', category: 'careers', en: 'Shape the future of digital advertising automation', ua: 'Формуйте майбутнє автоматизації цифрової реклами' },
  { key: 'careers.cta.text', category: 'careers', en: 'Ready to revolutionize Google Ads automation with us?', ua: 'Готові революціонізувати автоматизацію Google Ads разом з нами?' },
  { key: 'careers.cta.button', category: 'careers', en: 'Apply Now', ua: 'Подати Заявку' },
  { key: 'careers.positions.title', category: 'careers', en: 'Open Positions', ua: 'Відкриті Позиції' },

  // Common elements
  { key: 'common.enableTelegram', category: 'common', en: 'Enable Telegram Notifications', ua: 'Увімкнути Telegram Сповіщення' },
  { key: 'common.analyzeAndGenerate', category: 'common', en: 'Analyze & Generate', ua: 'Аналізувати та Генерувати' }
];

async function addMissingContent() {
  console.log('🔧 Adding missing content keys...');
  
  for (const item of missingContent) {
    try {
      // Insert content key
      const keyResult = await dbQuery(`
        INSERT INTO content_keys (key_name, category, description) 
        VALUES ($1, $2, $3)
        ON CONFLICT (key_name) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `, [item.key, item.category, `${item.category} content`]);

      const keyId = keyResult.rows[0].id;

      // Insert English translation
      await dbQuery(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        VALUES ($1, 'en', $2)
        ON CONFLICT (content_key_id, language_code) 
        DO UPDATE SET translation_text = EXCLUDED.translation_text, updated_at = CURRENT_TIMESTAMP
      `, [keyId, item.en]);

      // Insert Ukrainian translation
      await dbQuery(`
        INSERT INTO content_translations (content_key_id, language_code, translation_text)
        VALUES ($1, 'ua', $2)
        ON CONFLICT (content_key_id, language_code) 
        DO UPDATE SET translation_text = EXCLUDED.translation_text, updated_at = CURRENT_TIMESTAMP
      `, [keyId, item.ua]);

      console.log(`✅ Added: ${item.key}`);
    } catch (error) {
      console.error(`❌ Failed to add ${item.key}:`, error.message);
    }
  }

  console.log('🎉 Missing content added successfully!');
}

module.exports = { addMissingContent };

// Run if called directly
if (require.main === module) {
  addMissingContent().then(() => process.exit(0));
}