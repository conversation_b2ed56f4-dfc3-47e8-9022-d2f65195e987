import React from 'react';
import { Link, useParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import CampaignPerformance from './CampaignPerformance';
import AdPerformance from './AdPerformance';
import KeywordPerformance from './KeywordPerformance';
import BudgetMonitor from './BudgetMonitor';
import DeviceBidAdjuster from './DeviceBidAdjuster';
import SearchQueryPerformance from './SearchQueryPerformance';
import PerformanceMaxAssetAnalyzer from './PerformanceMaxAssetAnalyzer';
import KeywordConflictDetector from './KeywordConflictDetector';
import ScriptGenerator from './ScriptGenerator';
import AirtablePnlScriptGenerator from './AirtablePnlScriptGenerator';
import TelegramScriptGeneratorTool from './TelegramScriptGenerator';
import GAdsBudgetUpdaterTool from './GAdsBudgetUpdaterTool';

interface ToolData {
  [key: string]: {
    title: string;
    description: string;
  };
}

const toolData: ToolData = {
  'campaign-performance': {
    title: 'Campaign Performance by Type',
    description: 'Monitor and analyze your campaign performance across different campaign types.'
  },
  'ad-performance': {
    title: 'Ad Performance Analyzer',
    description: 'Analyze the performance of your ads across different metrics and dimensions.'
  },
  'keyword-performance': {
    title: 'Keyword Performance Monitor',
    description: 'Track and analyze the performance of your keywords over time.'
  },
  'budget-monitor': {
    title: 'Budget Pace Monitor',
    description: 'Monitor your campaign budget pacing and make adjustments as needed.'
  },
  'seasonal-budget': {
    title: 'Seasonal Budget Adjuster',
    description: 'Automatically adjust your budgets based on seasonal trends and patterns.'
  },
  'device-bid': {
    title: 'Device Bid Adjuster',
    description: 'Optimize your bids across different devices based on performance data.'
  },
  'search-query': {
    title: 'Search Query Analyzer',
    description: 'Analyze search queries that triggered your ads and identify optimization opportunities.'
  },
  'performance-max': {
    title: 'Performance Max Asset Analyzer',
    description: 'Analyze the performance of your assets in Performance Max campaigns.'
  },
  'keyword-conflict': {
    title: 'Keyword Conflict Detector',
    description: 'Identify and resolve conflicts between keywords in your account.'
  },
  'script-generator': {
    title: 'Search Ads Script Generator',
    description: 'Generate Google Ads scripts to create and manage search campaigns, ads, and keywords.'
  },
  'airtable-script': {
    title: 'Airtable P&L Script Generator',
    description: 'Generate a Google Ads script to sync performance data with Airtable and manage account status.'
  },
  'gads-budget-updater': {
    title: 'Google Ads Budget Updater Script Generator',
    description: 'Generate a Google Ads script to automatically increase campaign budgets and send Telegram notifications.'
  }
};

const ToolPlaceholder: React.FC = () => {
  const { toolId } = useParams<{ toolId: string }>();
  const tool = toolId ? toolData[toolId] : null;

  if (!tool) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h1 className="text-2xl font-bold text-red-600">Tool not found</h1>
        <p className="mt-4">The requested tool could not be found.</p>
        <Link to="/dashboard" className="inline-flex items-center mt-6 text-blue-600 hover:text-blue-800">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Dashboard
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <Link to="/dashboard" className="inline-flex items-center mb-6 text-blue-600 hover:text-blue-800">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Dashboard
      </Link>
      
      <div className="bg-white rounded-lg shadow-md p-8">
        <h1 className="text-3xl font-bold text-neutral-900 mb-4">{tool.title}</h1>
        <p className="text-lg text-neutral-600 mb-8">{tool.description}</p>
        
        {/* Conditionally render Tool or "Tool Coming Soon" */}
        {toolId === 'campaign-performance' ? (
          <CampaignPerformance />
        ) : toolId === 'ad-performance' ? (
          <AdPerformance />
        ) : toolId === 'keyword-performance' ? (
          <KeywordPerformance />
        ) : toolId === 'budget-monitor' ? (
          <BudgetMonitor />
        ) : toolId === 'device-bid' ? (
          <DeviceBidAdjuster />
        ) : toolId === 'search-query' ? (
          <SearchQueryPerformance />
        ) : toolId === 'performance-max' ? (
          <PerformanceMaxAssetAnalyzer />
        ) : toolId === 'keyword-conflict' ? (
          <KeywordConflictDetector />
        ) : toolId === 'script-generator' ? (
          <ScriptGenerator />
        ) : toolId === 'airtable-script' ? (
          <AirtablePnlScriptGenerator />
        ) : toolId === 'telegram-script-generator' ? (
          <TelegramScriptGeneratorTool />
        ) : toolId === 'gads-budget-updater' ? (
          <GAdsBudgetUpdaterTool />
        ) : (
          <div className="bg-blue-50 p-6 rounded-lg mb-8">
            <h2 className="text-xl font-semibold text-neutral-900 mb-4">Tool Coming Soon</h2>
            <p className="text-neutral-700">
              This tool is currently under development. Check back soon for updates!
            </p>
          </div>
        )}
        
        <div className="border-t border-gray-200 pt-6 mt-6">
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Other Tools You Might Like</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(toolData)
              .filter(([id]) => id !== toolId)
              .slice(0, 3)
              .map(([id, data]) => (
                <Link 
                  key={id} 
                  to={`/dashboard/${id}`}
                  className="block p-4 bg-white border border-gray-200 rounded-lg hover:bg-blue-50 transition-colors"
                >
                  <h4 className="font-medium text-blue-600">{data.title}</h4>
                </Link>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolPlaceholder;
