version: '3.8'

# This file provides production-specific overrides for the base docker-compose.yml.
# It introduces an Nginx reverse proxy and connects all services to a single network.

networks:
  gads-network:
    driver: bridge

services:
  postgres:
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-change-in-production}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    networks:
      - gads-network
    restart: always

  redis:
    volumes:
      - redis_prod_data:/data
    networks:
      - gads-network
    restart: always

  backend:
    environment:
      NODE_ENV: production
      DB_PASSWORD: ${POSTGRES_PASSWORD:-change-in-production}
      JWT_SECRET: ${JWT_SECRET:-change-this-super-secret-key-in-production}
      # The backend is not exposed publicly, so CORS can be more specific
      # to the Nginx service name within the Docker network.
      CORS_ORIGIN: http://nginx 
    ports: [] # Remove public port mapping, access will be via Nginx
    networks:
      - gads-network
    restart: always

  frontend:
    # In production, the frontend doesn't need to be exposed directly.
    # Nginx will serve its content.
    ports: [] # Remove public port mapping
    networks:
      - gads-network
    restart: always

  mailhog:
    networks:
      - gads-network
    ports:
      - "8025:8025" # Keep mailhog admin interface accessible if needed

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: gads-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # Use the corrected nginx configuration
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      # These paths are for SSL certificates and logs.
      # You would need to create these directories and place your SSL certs inside ./nginx/ssl
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - gads-network
    restart: always

volumes:
  postgres_prod_data:
  redis_prod_data:
