// Simple password hashing without dependencies
const crypto = require('crypto');

function simpleHash(password, salt) {
  return crypto.createHash('sha256').update(password + salt).digest('hex');
}

const salt = 'gads2024salt';
const admin = simpleHash('AdminPass2024Strong', salt);
const user = simpleHash('UserPass2024Complex', salt);
const demo = simpleHash('DemoPass2024Secure', salt);

console.log('Admin hash:', admin);
console.log('User hash:', user);
console.log('Demo hash:', demo);

// Create SQL with these hashes
const sql = `
-- Delete existing users
DELETE FROM users WHERE email LIKE '%@test.com' OR email LIKE '%@gads-supercharge.today';

-- Insert new users
INSERT INTO users (id, email, password_hash, role, preferred_language, created_at, updated_at) VALUES
  (gen_random_uuid(), '<EMAIL>', '${admin}', 'admin', 'en', NOW(), NOW()),
  (gen_random_uuid(), '<EMAIL>', '${user}', 'user', 'en', NOW(), NOW()),
  (gen_random_uuid(), '<EMAIL>', '${demo}', 'user', 'en', NOW(), NOW());

SELECT email, role FROM users WHERE email LIKE '%@gads-supercharge.today';
`;

console.log('\nSQL to execute:');
console.log(sql);